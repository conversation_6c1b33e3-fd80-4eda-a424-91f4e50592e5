import { historyService } from './api';

class ActivityLogger {
  constructor() {
    this.currentSession = this.generateSessionId();
    this.currentSource = 'silence'; // Track current audio source
    this.storeId = null;
    this.deviceId = 'web-player-' + Math.random().toString(36).substr(2, 9);
    this.offlineLogs = JSON.parse(localStorage.getItem('activityLogs') || '[]');
    this.lastActivity = null;
  }

  generateSessionId() {
    return 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }

  setStoreId(storeId) {
    this.storeId = storeId;
  }

  // Log when music player starts
  async logMusicPlayerStart(trackData, playlistData) {
    const previousSource = this.currentSource;
    this.currentSource = 'music_player';

    const activityData = {
      storeId: this.storeId,
      deviceId: this.deviceId,
      sessionId: this.currentSession,
      activityType: 'music_player_start',
      sourceFrom: previousSource,
      sourceTo: 'music_player',
      musicPlayerData: {
        trackId: trackData._id,
        playlistId: playlistData?._id,
        playlistName: playlistData?.name,
        trackTitle: trackData.title,
        trackArtist: trackData.artist,
        trackPosition: trackData.position || 0,
        isShuffled: trackData.isShuffled || false,
        isRepeating: trackData.isRepeating || false,
        volume: trackData.volume || 0.75
      },
      timestamp: new Date(),
      context: this.getContextData(),
      technical: this.getTechnicalData()
    };

    // If switching from radio, log the switch
    if (previousSource === 'radio_stream') {
      await this.logSourceSwitch(previousSource, 'music_player', {
        reason: 'user_initiated',
        musicPlayerData: activityData.musicPlayerData
      });
    }

    return this.logActivity(activityData);
  }

  // Log when music player stops
  async logMusicPlayerStop(trackData, duration) {
    const activityData = {
      storeId: this.storeId,
      deviceId: this.deviceId,
      sessionId: this.currentSession,
      activityType: 'music_player_stop',
      sourceFrom: 'music_player',
      sourceTo: 'silence',
      musicPlayerData: {
        trackId: trackData._id,
        trackTitle: trackData.title,
        trackArtist: trackData.artist,
        volume: trackData.volume || 0.75
      },
      duration: duration,
      timestamp: new Date(),
      context: this.getContextData(),
      technical: this.getTechnicalData()
    };

    this.currentSource = 'silence';
    return this.logActivity(activityData);
  }

  // Log when radio starts
  async logRadioStart(stationData) {
    const previousSource = this.currentSource;
    this.currentSource = 'radio_stream';

    const activityData = {
      storeId: this.storeId,
      deviceId: this.deviceId,
      sessionId: this.currentSession,
      activityType: 'radio_start',
      sourceFrom: previousSource,
      sourceTo: 'radio_stream',
      radioData: {
        stationId: stationData._id,
        stationName: stationData.name,
        stationUrl: stationData.url,
        stationGenre: stationData.genre,
        stationCountry: stationData.country,
        volume: stationData.volume || 0.75,
        streamQuality: stationData.quality || 'standard'
      },
      timestamp: new Date(),
      context: this.getContextData(),
      technical: this.getTechnicalData()
    };

    // If switching from music player, log the switch
    if (previousSource === 'music_player') {
      await this.logSourceSwitch(previousSource, 'radio_stream', {
        reason: 'user_initiated',
        radioData: activityData.radioData
      });
    }

    return this.logActivity(activityData);
  }

  // Log when radio stops
  async logRadioStop(stationData, duration) {
    const activityData = {
      storeId: this.storeId,
      deviceId: this.deviceId,
      sessionId: this.currentSession,
      activityType: 'radio_stop',
      sourceFrom: 'radio_stream',
      sourceTo: 'silence',
      radioData: {
        stationId: stationData._id,
        stationName: stationData.name,
        stationUrl: stationData.url,
        stationGenre: stationData.genre,
        stationCountry: stationData.country,
        volume: stationData.volume || 0.75
      },
      duration: duration,
      timestamp: new Date(),
      context: this.getContextData(),
      technical: this.getTechnicalData()
    };

    this.currentSource = 'silence';
    return this.logActivity(activityData);
  }

  // Log source switching
  async logSourceSwitch(fromSource, toSource, additionalData = {}) {
    const activityData = {
      storeId: this.storeId,
      deviceId: this.deviceId,
      sessionId: this.currentSession,
      activityType: 'source_switch',
      sourceFrom: fromSource,
      sourceTo: toSource,
      timestamp: new Date(),
      context: {
        ...this.getContextData(),
        switchReason: additionalData.reason || 'user_initiated',
        manualTrigger: true
      },
      technical: this.getTechnicalData(),
      ...additionalData
    };

    return this.logActivity(activityData);
  }

  // Log volume changes
  async logVolumeChange(newVolume, source) {
    const activityData = {
      storeId: this.storeId,
      deviceId: this.deviceId,
      sessionId: this.currentSession,
      activityType: 'volume_change',
      sourceFrom: source,
      sourceTo: source,
      timestamp: new Date(),
      context: {
        ...this.getContextData(),
        previousVolume: this.lastActivity?.volume,
        newVolume: newVolume
      }
    };

    if (source === 'music_player') {
      activityData.musicPlayerData = { volume: newVolume };
    } else if (source === 'radio_stream') {
      activityData.radioData = { volume: newVolume };
    }

    return this.logActivity(activityData);
  }

  // Log mode switching between music player and radio
  async logModeSwitch(fromSource, toSource, additionalData = {}) {
    const activityData = {
      storeId: this.storeId,
      deviceId: this.deviceId,
      sessionId: this.currentSession,
      activityType: 'source_switch',
      sourceFrom: fromSource,
      sourceTo: toSource,
      timestamp: new Date(),
      context: {
        ...this.getContextData(),
        switchReason: additionalData.reason || 'user_initiated',
        previousSource: this.currentSource,
        manualTrigger: true,
        automaticTrigger: false
      },
      technical: this.getTechnicalData()
    };

    // Add specific data based on the switch direction
    if (fromSource === 'music_player' && additionalData.currentTrack) {
      activityData.musicPlayerData = {
        trackId: additionalData.currentTrack._id,
        trackTitle: additionalData.currentTrack.title,
        trackArtist: additionalData.currentTrack.artist,
        playlistId: additionalData.playlistId,
        playlistName: additionalData.playlistName,
        wasPlaying: additionalData.wasPlaying || false
      };
    }

    if (fromSource === 'radio_stream' && additionalData.currentStation) {
      activityData.radioData = {
        stationId: additionalData.currentStation._id,
        stationName: additionalData.currentStation.name,
        stationGenre: additionalData.currentStation.genre,
        stationCountry: additionalData.currentStation.country,
        wasPlaying: additionalData.wasPlaying || false
      };
    }

    // Update current source
    this.currentSource = toSource;

    return this.logActivity(activityData);
  }

  // Generic activity logging
  async logActivity(activityData) {
    try {
      // Ensure storeId is set before logging
      if (!this.storeId) {
        console.warn('Cannot log activity - storeId not set. Call setStoreId() first.');
        return;
      }

      // Ensure storeId is included in activity data
      activityData.storeId = this.storeId;

      // Add compliance requirements
      activityData.compliance = {
        requiresReporting: this.requiresCompliance(activityData.activityType),
        reportedToSAMRO: false,
        reportedToSAMPRA: false,
        reportedToRISA: false
      };

      // Add audit info
      activityData.auditInfo = {
        verificationStatus: 'pending',
        tags: this.generateTags(activityData)
      };

      await historyService.logActivity(activityData);
      this.lastActivity = activityData;
      console.log('Activity logged:', activityData.activityType);
    } catch (error) {
      console.error('Failed to log activity, storing offline:', error);
      this.offlineLogs.push(activityData);
      localStorage.setItem('activityLogs', JSON.stringify(this.offlineLogs));
    }
  }

  // Sync offline logs
  async syncOfflineLogs() {
    if (this.offlineLogs.length === 0) return;

    try {
      await historyService.syncOfflineActivities(this.offlineLogs);
      this.offlineLogs = [];
      localStorage.removeItem('activityLogs');
      console.log('Offline activity logs synced successfully');
    } catch (error) {
      console.error('Failed to sync offline activity logs:', error);
    }
  }

  // Helper methods
  getContextData() {
    return {
      userAgent: navigator.userAgent,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      storeHours: {
        isOpen: this.isStoreOpen(),
        openTime: '09:00',
        closeTime: '18:00'
      },
      manualTrigger: true,
      automaticTrigger: false
    };
  }

  getTechnicalData() {
    return {
      connectionType: navigator.connection?.effectiveType || 'unknown',
      audioFormat: 'mp3',
      bitrate: '128kbps',
      sampleRate: '44.1kHz'
    };
  }

  requiresCompliance(activityType) {
    const complianceRequired = [
      'music_player_start',
      'music_player_stop',
      'radio_start',
      'radio_stop',
      'source_switch'
    ];
    return complianceRequired.includes(activityType);
  }

  generateTags(activityData) {
    const tags = [];
    if (activityData.activityType.includes('switch')) tags.push('source_switch');
    if (activityData.activityType.includes('music')) tags.push('music_player');
    if (activityData.activityType.includes('radio')) tags.push('radio_stream');
    if (activityData.context?.manualTrigger) tags.push('manual');
    return tags;
  }

  isStoreOpen() {
    const now = new Date();
    const hour = now.getHours();
    return hour >= 9 && hour < 18; // Simple store hours check
  }

  getCurrentSource() {
    return this.currentSource;
  }

  startNewSession() {
    this.currentSession = this.generateSessionId();
  }

  // Log when Spotify playback starts
  async logSpotifyStart(trackData, playbackData) {
    const previousSource = this.currentSource;
    this.currentSource = 'spotify';

    const activityData = {
      storeId: this.storeId,
      deviceId: this.deviceId,
      sessionId: this.currentSession,
      activityType: 'spotify_start',
      sourceFrom: previousSource,
      sourceTo: 'spotify',
      spotifyData: {
        trackId: trackData.id,
        trackUri: trackData.uri,
        trackName: trackData.name,
        artists: trackData.artists.map(artist => ({
          id: artist.id,
          name: artist.name,
          uri: artist.uri
        })),
        album: {
          id: trackData.album.id,
          name: trackData.album.name,
          uri: trackData.album.uri,
          images: trackData.album.images
        },
        duration: trackData.duration_ms,
        popularity: trackData.popularity,
        explicit: trackData.explicit,
        isrc: trackData.external_ids?.isrc,
        previewUrl: trackData.preview_url,
        playbackContext: {
          type: playbackData.context?.type,
          uri: playbackData.context?.uri,
          href: playbackData.context?.href
        },
        device: {
          id: playbackData.device?.id,
          name: playbackData.device?.name,
          type: playbackData.device?.type,
          volume: playbackData.device?.volume_percent
        },
        shuffleState: playbackData.shuffle_state,
        repeatState: playbackData.repeat_state,
        progressMs: playbackData.progress_ms
      },
      timestamp: new Date(),
      context: this.getContextData(),
      technical: this.getTechnicalData()
    };

    // If switching from another source, log the transition
    if (previousSource !== 'silence' && previousSource !== 'spotify') {
      await this.logSourceSwitch(previousSource, 'spotify');
    }

    await this.logActivity(activityData);
  }

  // Log when Spotify playback stops
  async logSpotifyStop(trackData, playbackData) {
    const activityData = {
      storeId: this.storeId,
      deviceId: this.deviceId,
      sessionId: this.currentSession,
      activityType: 'spotify_stop',
      sourceFrom: 'spotify',
      sourceTo: 'silence',
      spotifyData: {
        trackId: trackData.id,
        trackUri: trackData.uri,
        trackName: trackData.name,
        artists: trackData.artists.map(artist => artist.name),
        duration: trackData.duration_ms,
        playedDuration: playbackData.progress_ms,
        completionPercentage: Math.round((playbackData.progress_ms / trackData.duration_ms) * 100),
        isrc: trackData.external_ids?.isrc,
        device: {
          id: playbackData.device?.id,
          name: playbackData.device?.name,
          type: playbackData.device?.type
        }
      },
      timestamp: new Date(),
      context: this.getContextData(),
      technical: this.getTechnicalData()
    };

    this.currentSource = 'silence';
    await this.logActivity(activityData);
  }

  // Log Spotify playback control actions
  async logSpotifyControl(action, trackData, playbackData) {
    const activityData = {
      storeId: this.storeId,
      deviceId: this.deviceId,
      sessionId: this.currentSession,
      activityType: 'spotify_control',
      sourceFrom: 'spotify',
      sourceTo: 'spotify',
      spotifyData: {
        action, // 'play', 'pause', 'seek', 'volume', 'shuffle', 'repeat'
        trackId: trackData?.id,
        trackName: trackData?.name,
        artists: trackData?.artists?.map(artist => artist.name),
        isrc: trackData?.external_ids?.isrc,
        progressMs: playbackData?.progress_ms,
        device: {
          id: playbackData?.device?.id,
          name: playbackData?.device?.name,
          volume: playbackData?.device?.volume_percent
        },
        shuffleState: playbackData?.shuffle_state,
        repeatState: playbackData?.repeat_state
      },
      timestamp: new Date(),
      context: this.getContextData(),
      technical: this.getTechnicalData()
    };

    await this.logActivity(activityData);
  }
}

// Create singleton instance
const activityLogger = new ActivityLogger();
export default activityLogger;
