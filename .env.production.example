# Production Environment Configuration for TrakSong

# Environment
NODE_ENV=production

# Server Configuration
PORT=5000
FRONTEND_URL=https://yourdomain.com

# Database Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/traksong?retryWrites=true&w=majority

# Redis Configuration (ElastiCache endpoint)
REDIS_URL=redis://traksong-production-redis.abc123.cache.amazonaws.com:6379

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-minimum-256-bits-change-this-in-production

# Email Configuration (Brevo SMTP)
EMAIL_SERVICE=brevo
EMAIL_HOST=smtp-relay.brevo.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-brevo-smtp-api-key
EMAIL_FROM=TrakSong System <<EMAIL>>

# AWS Configuration (for S3, CloudFront, etc.)
AWS_REGION=af-south-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
S3_BUCKET_AUDIO=traksong-production-audio-files
S3_BUCKET_REPORTS=traksong-production-reports
S3_BUCKET_BACKUPS=traksong-production-backups
CLOUDFRONT_DOMAIN=d123456789abcdef.cloudfront.net

# Compliance API Configuration
SAMRO_API_URL=https://api.samro.org.za
SAMRO_API_KEY=your-samro-api-key
SAMPRA_API_URL=https://api.sampra.org.za
SAMPRA_API_KEY=your-sampra-api-key
RISA_API_URL=https://api.risa.org.za
RISA_API_KEY=your-risa-api-key

# Spotify Integration (Production)
SPOTIFY_CLIENT_ID=your-production-spotify-client-id
SPOTIFY_CLIENT_SECRET=your-production-spotify-client-secret
SPOTIFY_REDIRECT_URI=https://yourdomain.com/spotify/callback

# Encryption Key (Production - Use strong random key)
ENCRYPTION_KEY=your-strong-production-encryption-key

# Monitoring and Logging
LOG_LEVEL=info
SENTRY_DSN=https://<EMAIL>/project-id
NEW_RELIC_LICENSE_KEY=your-new-relic-license-key

# Security Configuration
CORS_ORIGIN=https://yourdomain.com,https://www.yourdomain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# File Upload Configuration
MAX_FILE_SIZE=52428800
ALLOWED_AUDIO_FORMATS=mp3,wav,flac,aac
UPLOAD_PATH=/app/uploads

# Session Configuration
SESSION_SECRET=your-session-secret-change-this
SESSION_TIMEOUT=3600000

# WebSocket Configuration
WS_PORT=8080
WS_HEARTBEAT_INTERVAL=30000

# Worker Configuration
WORKER_CONCURRENCY=5
QUEUE_REDIS_URL=redis://traksong-production-redis.abc123.cache.amazonaws.com:6379

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Performance Configuration
CLUSTER_MODE=true
CLUSTER_WORKERS=auto
MEMORY_LIMIT=4096
CPU_LIMIT=2000

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_REAL_TIME_MONITORING=true
ENABLE_COMPLIANCE_AUTOMATION=true
ENABLE_AUTO_SCALING=true

# Third-party Integrations
STRIPE_SECRET_KEY=sk_live_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX-X

# Compliance Settings
COMPLIANCE_REPORT_SCHEDULE=0 2 * * *
AUTO_COMPLIANCE_VALIDATION=true
COMPLIANCE_ALERT_EMAIL=<EMAIL>

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
ENABLE_QUERY_CACHE=true

# API Rate Limiting
API_RATE_LIMIT=1000
API_BURST_LIMIT=2000
API_WINDOW_SIZE=3600

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_RETRIES=3

# Scaling Configuration
AUTO_SCALE_CPU_THRESHOLD=70
AUTO_SCALE_MEMORY_THRESHOLD=80
AUTO_SCALE_MIN_INSTANCES=10
AUTO_SCALE_MAX_INSTANCES=1000

# Database Connection Pool
DB_POOL_SIZE=20
DB_POOL_TIMEOUT=30000
DB_POOL_IDLE_TIMEOUT=300000

# Redis Connection Pool
REDIS_POOL_SIZE=20
REDIS_POOL_TIMEOUT=5000
REDIS_POOL_RETRY_ATTEMPTS=3

# Logging Configuration
LOG_FORMAT=json
LOG_ROTATION=daily
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=30

# Security Headers
ENABLE_HELMET=true
ENABLE_CORS=true
ENABLE_RATE_LIMITING=true
ENABLE_CSRF_PROTECTION=true

# Content Security Policy
CSP_DEFAULT_SRC='self'
CSP_SCRIPT_SRC='self' 'unsafe-inline' https://cdn.jsdelivr.net
CSP_STYLE_SRC='self' 'unsafe-inline' https://fonts.googleapis.com
CSP_FONT_SRC='self' https://fonts.gstatic.com
CSP_IMG_SRC='self' data: https:
CSP_MEDIA_SRC='self' https:

# SSL Configuration
SSL_REDIRECT=true
HSTS_MAX_AGE=31536000
HSTS_INCLUDE_SUBDOMAINS=true

# Timezone Configuration
TZ=Africa/Johannesburg
DEFAULT_TIMEZONE=Africa/Johannesburg

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=System is under maintenance. Please try again later.

# Debug Configuration (disable in production)
DEBUG=false
VERBOSE_LOGGING=false
ENABLE_PROFILING=false
