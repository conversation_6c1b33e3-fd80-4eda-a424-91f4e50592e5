import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  IconButton,
  Slider,
  Avatar,
  Chip,
  LinearProgress,
  Alert,
  Tooltip,
  Grid
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  SkipNext as NextIcon,
  SkipPrevious as PrevIcon,
  VolumeUp as VolumeIcon,
  Shuffle as ShuffleIcon,
  Repeat as RepeatIcon,
  RepeatOne as RepeatOneIcon,
  QueueMusic as QueueIcon,
  Devices as DevicesIcon
} from '@mui/icons-material';
import spotifyService from '../services/spotifyService';
import activityLogger from '../services/activityLogger';

const SpotifyPlayer = () => {
  const [playback, setPlayback] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [volume, setVolume] = useState(50);
  const [progress, setProgress] = useState(0);
  const [seeking, setSeeking] = useState(false);
  const intervalRef = useRef(null);

  useEffect(() => {
    loadPlaybackState();
    startProgressTracking();
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const loadPlaybackState = async () => {
    try {
      setLoading(true);
      const playbackData = await spotifyService.getPlayback();
      setPlayback(playbackData);
      
      if (playbackData?.device?.volume_percent !== undefined) {
        setVolume(playbackData.device.volume_percent);
      }
      
      if (playbackData?.progress_ms && playbackData?.item?.duration_ms) {
        setProgress((playbackData.progress_ms / playbackData.item.duration_ms) * 100);
      }
      
      setError(null);
    } catch (error) {
      console.error('Failed to load playback state:', error);
      setError('Failed to load playback state');
    } finally {
      setLoading(false);
    }
  };

  const startProgressTracking = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(async () => {
      try {
        const playbackData = await spotifyService.getPlayback();
        if (playbackData && !seeking) {
          setPlayback(playbackData);
          
          if (playbackData.progress_ms && playbackData.item?.duration_ms) {
            setProgress((playbackData.progress_ms / playbackData.item.duration_ms) * 100);
          }
        }
      } catch (error) {
        console.error('Failed to update playback state:', error);
      }
    }, 1000);
  };

  const handlePlayPause = async () => {
    try {
      const action = playback?.is_playing ? 'pause' : 'play';

      if (playback?.is_playing) {
        await spotifyService.pause();
      } else {
        await spotifyService.play();
      }

      // Update state immediately for better UX
      setPlayback(prev => prev ? { ...prev, is_playing: !prev.is_playing } : null);

      // Log the action for compliance
      if (playback?.item) {
        await activityLogger.logSpotifyControl(action, playback.item, playback);
      }
      await spotifyService.logCurrentTrack();
    } catch (error) {
      console.error('Failed to toggle playback:', error);
      setError('Failed to control playback');
    }
  };

  const handleNext = async () => {
    try {
      const currentTrack = playback?.item;
      await spotifyService.next();

      // Log the control action
      if (currentTrack) {
        await activityLogger.logSpotifyControl('next', currentTrack, playback);
      }

      setTimeout(loadPlaybackState, 500); // Delay to allow Spotify to update
    } catch (error) {
      console.error('Failed to skip to next track:', error);
      setError('Failed to skip track');
    }
  };

  const handlePrevious = async () => {
    try {
      const currentTrack = playback?.item;
      await spotifyService.previous();

      // Log the control action
      if (currentTrack) {
        await activityLogger.logSpotifyControl('previous', currentTrack, playback);
      }

      setTimeout(loadPlaybackState, 500);
    } catch (error) {
      console.error('Failed to skip to previous track:', error);
      setError('Failed to skip track');
    }
  };

  const handleVolumeChange = async (event, newValue) => {
    setVolume(newValue);
    try {
      await spotifyService.setVolume(newValue);
    } catch (error) {
      console.error('Failed to set volume:', error);
    }
  };

  const handleProgressChange = (event, newValue) => {
    setProgress(newValue);
    setSeeking(true);
  };

  const handleProgressCommit = async (event, newValue) => {
    if (playback?.item?.duration_ms) {
      const positionMs = Math.round((newValue / 100) * playback.item.duration_ms);
      try {
        await spotifyService.seek(positionMs);
      } catch (error) {
        console.error('Failed to seek:', error);
      }
    }
    setSeeking(false);
  };

  const handleShuffle = async () => {
    try {
      const newState = !playback?.shuffle_state;
      await spotifyService.setShuffle(newState);
      setPlayback(prev => prev ? { ...prev, shuffle_state: newState } : null);
    } catch (error) {
      console.error('Failed to toggle shuffle:', error);
    }
  };

  const handleRepeat = async () => {
    try {
      const currentState = playback?.repeat_state || 'off';
      const nextState = currentState === 'off' ? 'context' : 
                       currentState === 'context' ? 'track' : 'off';
      
      await spotifyService.setRepeat(nextState);
      setPlayback(prev => prev ? { ...prev, repeat_state: nextState } : null);
    } catch (error) {
      console.error('Failed to toggle repeat:', error);
    }
  };

  const formatTime = (ms) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getRepeatIcon = () => {
    switch (playback?.repeat_state) {
      case 'track':
        return <RepeatOneIcon />;
      case 'context':
        return <RepeatIcon color="primary" />;
      default:
        return <RepeatIcon />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <LinearProgress />
          <Typography variant="body2" color="text.secondary" mt={1}>
            Loading Spotify player...
          </Typography>
        </CardContent>
      </Card>
    );
  }

  if (!playback || !playback.item) {
    return (
      <Card>
        <CardContent>
          <Alert severity="info">
            No music currently playing on Spotify. Start playing music in your Spotify app to see controls here.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const track = playback.item;
  const artists = track.artists.map(artist => artist.name).join(', ');
  const albumImage = track.album.images?.[0]?.url;

  return (
    <Card>
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <Grid container spacing={2} alignItems="center">
          {/* Track Info */}
          <Grid item xs={12} md={4}>
            <Box display="flex" alignItems="center" gap={2}>
              <Avatar
                src={albumImage}
                variant="rounded"
                sx={{ width: 56, height: 56 }}
              >
                <QueueIcon />
              </Avatar>
              <Box flex={1} minWidth={0}>
                <Typography variant="subtitle1" noWrap>
                  {track.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" noWrap>
                  {artists}
                </Typography>
                <Typography variant="caption" color="text.secondary" noWrap>
                  {track.album.name}
                </Typography>
              </Box>
            </Box>
          </Grid>

          {/* Player Controls */}
          <Grid item xs={12} md={4}>
            <Box display="flex" flexDirection="column" alignItems="center">
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <Tooltip title="Shuffle">
                  <IconButton 
                    onClick={handleShuffle}
                    color={playback.shuffle_state ? "primary" : "default"}
                    size="small"
                  >
                    <ShuffleIcon />
                  </IconButton>
                </Tooltip>
                
                <IconButton onClick={handlePrevious}>
                  <PrevIcon />
                </IconButton>
                
                <IconButton 
                  onClick={handlePlayPause}
                  sx={{ 
                    backgroundColor: 'primary.main',
                    color: 'white',
                    '&:hover': { backgroundColor: 'primary.dark' }
                  }}
                >
                  {playback.is_playing ? <PauseIcon /> : <PlayIcon />}
                </IconButton>
                
                <IconButton onClick={handleNext}>
                  <NextIcon />
                </IconButton>
                
                <Tooltip title="Repeat">
                  <IconButton onClick={handleRepeat} size="small">
                    {getRepeatIcon()}
                  </IconButton>
                </Tooltip>
              </Box>

              {/* Progress Bar */}
              <Box display="flex" alignItems="center" gap={1} width="100%">
                <Typography variant="caption" color="text.secondary">
                  {formatTime(playback.progress_ms || 0)}
                </Typography>
                <Slider
                  value={progress}
                  onChange={handleProgressChange}
                  onChangeCommitted={handleProgressCommit}
                  size="small"
                  sx={{ flex: 1 }}
                />
                <Typography variant="caption" color="text.secondary">
                  {formatTime(track.duration_ms)}
                </Typography>
              </Box>
            </Box>
          </Grid>

          {/* Volume and Device */}
          <Grid item xs={12} md={4}>
            <Box display="flex" alignItems="center" justifyContent="flex-end" gap={2}>
              <Box display="flex" alignItems="center" gap={1} minWidth={120}>
                <VolumeIcon />
                <Slider
                  value={volume}
                  onChange={handleVolumeChange}
                  size="small"
                  sx={{ flex: 1 }}
                />
              </Box>
              
              {playback.device && (
                <Tooltip title={`Playing on ${playback.device.name}`}>
                  <Chip
                    icon={<DevicesIcon />}
                    label={playback.device.name}
                    size="small"
                    variant="outlined"
                  />
                </Tooltip>
              )}
            </Box>
          </Grid>
        </Grid>

        {/* Track metadata for compliance */}
        {track.external_ids?.isrc && (
          <Box mt={2} pt={2} borderTop={1} borderColor="divider">
            <Typography variant="caption" color="text.secondary">
              ISRC: {track.external_ids.isrc} | 
              Popularity: {track.popularity}/100 |
              {track.explicit && ' Explicit Content |'}
              Duration: {formatTime(track.duration_ms)}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default SpotifyPlayer;
