# Database
MONGO_URI=mongodb://localhost:27017/traksong

# JWT Secret
JWT_SECRET=your_jwt_secret_here

# Server Port
PORT=5000

# Email Configuration
# Options: gmail, brevo, ethereal, or any nodemailer supported service
EMAIL_SERVICE=brevo
EMAIL_HOST=smtp-relay.brevo.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_brevo_smtp_key
EMAIL_FROM=TrakSong System <<EMAIL>>

# Frontend URL (for email links)
FRONTEND_URL=http://localhost:3000

# Spotify Integration
SPOTIFY_CLIENT_ID=13d58096e529425f9f98fceba04de161
SPOTIFY_CLIENT_SECRET=dc1499ec2fc946ad8a5fc97102fa600b
SPOTIFY_REDIRECT_URI=http://localhost:3000/store

# Encryption Key (for storing sensitive tokens)
ENCRYPTION_KEY=your_encryption_key_change_in_production

# Environment
NODE_ENV=development
