version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: traksong-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-admin123}
      MONGO_INITDB_DATABASE: traksong
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - traksong-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/traksong --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: traksong-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - traksong-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TrakSong API
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
      target: production
    container_name: traksong-api
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGO_URI: mongodb://admin:${MONGO_ROOT_PASSWORD:-admin123}@mongodb:27017/traksong?authSource=admin
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis123}@redis:6379
      EMAIL_SERVICE: ${EMAIL_SERVICE:-brevo}
      EMAIL_HOST: ${EMAIL_HOST:-smtp-relay.brevo.com}
      EMAIL_PORT: ${EMAIL_PORT:-587}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASSWORD: ${EMAIL_PASSWORD}
      EMAIL_FROM: ${EMAIL_FROM:-TrakSong System <<EMAIL>>}
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost:3000}
      SPOTIFY_CLIENT_ID: ${SPOTIFY_CLIENT_ID}
      SPOTIFY_CLIENT_SECRET: ${SPOTIFY_CLIENT_SECRET}
      SPOTIFY_REDIRECT_URI: ${SPOTIFY_REDIRECT_URI:-http://localhost:3000/spotify/callback}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY:-change-this-in-production}
    ports:
      - "5000:5000"
    volumes:
      - uploads_data:/app/uploads
      - reports_data:/app/reports
      - logs_data:/app/logs
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - traksong-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:5000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TrakSong UI
  ui:
    build:
      context: .
      dockerfile: Dockerfile.ui
      target: production
    container_name: traksong-ui
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      api:
        condition: service_healthy
    networks:
      - traksong-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  reports_data:
    driver: local
  logs_data:
    driver: local

networks:
  traksong-network:
    driver: bridge
