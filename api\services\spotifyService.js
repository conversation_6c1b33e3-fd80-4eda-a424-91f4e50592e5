const axios = require('axios');
const crypto = require('crypto');
const User = require('../models/User.model');
const PlayHistory = require('../models/PlayHistory.model');
const Track = require('../models/Track.model');

class SpotifyService {
  constructor() {
    this.clientId = process.env.SPOTIFY_CLIENT_ID;
    this.clientSecret = process.env.SPOTIFY_CLIENT_SECRET;
    this.redirectUri = process.env.SPOTIFY_REDIRECT_URI;
    this.baseUrl = 'https://api.spotify.com/v1';
    this.authUrl = 'https://accounts.spotify.com';
    this.encryptionKey = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';
  }

  // Encryption/Decryption for tokens
  encrypt(text) {
    if (!text) return null;
    const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }

  decrypt(encryptedText) {
    if (!encryptedText) return null;
    try {
      const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
      let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      console.error('Token decryption failed:', error);
      return null;
    }
  }

  // Generate authorization URL for OAuth flow
  generateAuthUrl(storeId, scopes = []) {
    const defaultScopes = [
      'user-read-playback-state',
      'user-modify-playback-state',
      'user-read-currently-playing',
      'user-read-recently-played',
      'playlist-read-private',
      'playlist-read-collaborative',
      'user-library-read',
      'streaming'
    ];
    
    const allScopes = [...new Set([...defaultScopes, ...scopes])];
    const state = crypto.randomBytes(16).toString('hex') + ':' + storeId;
    
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: this.clientId,
      scope: allScopes.join(' '),
      redirect_uri: this.redirectUri,
      state: state,
      show_dialog: 'true'
    });

    return `${this.authUrl}/authorize?${params.toString()}`;
  }

  // Exchange authorization code for access token
  async exchangeCodeForToken(code, state) {
    try {
      const [, storeId] = state.split(':');
      
      const response = await axios.post(`${this.authUrl}/api/token`, {
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: this.redirectUri,
        client_id: this.clientId,
        client_secret: this.clientSecret
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const { access_token, refresh_token, expires_in, scope } = response.data;
      
      // Get user profile from Spotify
      const profileResponse = await axios.get(`${this.baseUrl}/me`, {
        headers: { Authorization: `Bearer ${access_token}` }
      });

      const profile = profileResponse.data;

      // Update user with Spotify integration data
      await User.findByIdAndUpdate(storeId, {
        'spotifyIntegration.isConnected': true,
        'spotifyIntegration.spotifyUserId': profile.id,
        'spotifyIntegration.displayName': profile.display_name,
        'spotifyIntegration.email': profile.email,
        'spotifyIntegration.country': profile.country,
        'spotifyIntegration.product': profile.product,
        'spotifyIntegration.accessToken': this.encrypt(access_token),
        'spotifyIntegration.refreshToken': this.encrypt(refresh_token),
        'spotifyIntegration.tokenExpiry': new Date(Date.now() + expires_in * 1000),
        'spotifyIntegration.scope': scope,
        'spotifyIntegration.connectionDate': new Date(),
        'spotifyIntegration.lastSync': new Date()
      });

      return {
        success: true,
        user: profile,
        expiresIn: expires_in
      };
    } catch (error) {
      console.error('Spotify token exchange failed:', error.response?.data || error.message);
      throw new Error('Failed to connect Spotify account');
    }
  }

  // Refresh access token
  async refreshAccessToken(userId) {
    try {
      const user = await User.findById(userId);
      if (!user?.spotifyIntegration?.refreshToken) {
        throw new Error('No refresh token available');
      }

      const refreshToken = this.decrypt(user.spotifyIntegration.refreshToken);
      
      const response = await axios.post(`${this.authUrl}/api/token`, {
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: this.clientId,
        client_secret: this.clientSecret
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const { access_token, expires_in, refresh_token: newRefreshToken } = response.data;

      // Update tokens
      const updateData = {
        'spotifyIntegration.accessToken': this.encrypt(access_token),
        'spotifyIntegration.tokenExpiry': new Date(Date.now() + expires_in * 1000),
        'spotifyIntegration.lastSync': new Date()
      };

      if (newRefreshToken) {
        updateData['spotifyIntegration.refreshToken'] = this.encrypt(newRefreshToken);
      }

      await User.findByIdAndUpdate(userId, updateData);

      return access_token;
    } catch (error) {
      console.error('Token refresh failed:', error.response?.data || error.message);
      throw new Error('Failed to refresh Spotify token');
    }
  }

  // Get valid access token (refresh if needed)
  async getValidAccessToken(userId) {
    const user = await User.findById(userId);
    if (!user?.spotifyIntegration?.isConnected) {
      throw new Error('Spotify not connected');
    }

    const tokenExpiry = user.spotifyIntegration.tokenExpiry;
    const now = new Date();

    // If token expires in less than 5 minutes, refresh it
    if (!tokenExpiry || tokenExpiry.getTime() - now.getTime() < 5 * 60 * 1000) {
      return await this.refreshAccessToken(userId);
    }

    return this.decrypt(user.spotifyIntegration.accessToken);
  }

  // Make authenticated request to Spotify API
  async makeSpotifyRequest(userId, endpoint, method = 'GET', data = null) {
    try {
      const accessToken = await this.getValidAccessToken(userId);
      
      const config = {
        method,
        url: `${this.baseUrl}${endpoint}`,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        config.data = data;
      }

      const response = await axios(config);
      return response.data;
    } catch (error) {
      if (error.response?.status === 401) {
        // Token might be invalid, try refreshing once
        try {
          const newToken = await this.refreshAccessToken(userId);
          const config = {
            method,
            url: `${this.baseUrl}${endpoint}`,
            headers: {
              'Authorization': `Bearer ${newToken}`,
              'Content-Type': 'application/json'
            }
          };

          if (data && (method === 'POST' || method === 'PUT')) {
            config.data = data;
          }

          const response = await axios(config);
          return response.data;
        } catch (refreshError) {
          throw new Error('Spotify authentication failed');
        }
      }
      throw error;
    }
  }

  // Get current playback state
  async getCurrentPlayback(userId) {
    try {
      return await this.makeSpotifyRequest(userId, '/me/player');
    } catch (error) {
      if (error.response?.status === 204) {
        return null; // No active device
      }
      throw error;
    }
  }

  // Get currently playing track
  async getCurrentlyPlaying(userId) {
    try {
      return await this.makeSpotifyRequest(userId, '/me/player/currently-playing');
    } catch (error) {
      if (error.response?.status === 204) {
        return null; // Nothing playing
      }
      throw error;
    }
  }

  // Get user's devices
  async getDevices(userId) {
    return await this.makeSpotifyRequest(userId, '/me/player/devices');
  }

  // Control playback
  async controlPlayback(userId, action, data = {}) {
    const endpoints = {
      play: '/me/player/play',
      pause: '/me/player/pause',
      next: '/me/player/next',
      previous: '/me/player/previous',
      seek: '/me/player/seek',
      volume: '/me/player/volume',
      shuffle: '/me/player/shuffle',
      repeat: '/me/player/repeat'
    };

    const endpoint = endpoints[action];
    if (!endpoint) {
      throw new Error(`Invalid playback action: ${action}`);
    }

    let method = 'PUT';
    let requestData = null;

    switch (action) {
      case 'play':
        method = 'PUT';
        requestData = data; // Can include context_uri, uris, offset
        break;
      case 'next':
      case 'previous':
        method = 'POST';
        break;
      case 'seek':
        return await this.makeSpotifyRequest(userId, `${endpoint}?position_ms=${data.position_ms}`, 'PUT');
      case 'volume':
        return await this.makeSpotifyRequest(userId, `${endpoint}?volume_percent=${data.volume_percent}`, 'PUT');
      case 'shuffle':
        return await this.makeSpotifyRequest(userId, `${endpoint}?state=${data.state}`, 'PUT');
      case 'repeat':
        return await this.makeSpotifyRequest(userId, `${endpoint}?state=${data.state}`, 'PUT');
    }

    return await this.makeSpotifyRequest(userId, endpoint, method, requestData);
  }

  // Log current playing track for compliance
  async logCurrentTrack(userId, storeId) {
    try {
      const playbackState = await this.getCurrentPlayback(userId);
      if (!playbackState || !playbackState.item) {
        return null; // Nothing playing
      }

      const track = playbackState.item;
      const device = playbackState.device;

      // Check if this track is already logged recently (within last 30 seconds)
      const recentLog = await PlayHistory.findOne({
        storeId: storeId,
        'spotifyData.trackId': track.id,
        startTime: { $gte: new Date(Date.now() - 30000) }
      });

      if (recentLog) {
        return recentLog; // Already logged recently
      }

      // Create or find corresponding Track record
      let trackRecord = await Track.findOne({ 'metadata.spotifyId': track.id });
      if (!trackRecord) {
        // Create new track record from Spotify data
        trackRecord = new Track({
          title: track.name,
          artist: track.artists.map(a => a.name).join(', '),
          album: track.album.name,
          duration: Math.round(track.duration_ms / 1000),
          genre: track.album.genres?.[0] || 'Unknown',
          metadata: {
            spotifyId: track.id,
            spotifyUri: track.uri,
            isrc: track.external_ids?.isrc,
            popularity: track.popularity,
            explicit: track.explicit,
            previewUrl: track.preview_url,
            albumId: track.album.id,
            artistIds: track.artists.map(a => a.id)
          },
          source: 'spotify',
          uploadedBy: userId
        });
        await trackRecord.save();
      }

      // Create play history entry
      const playHistory = new PlayHistory({
        storeId: storeId,
        deviceId: device.id || 'spotify-device',
        trackId: trackRecord._id,
        startTime: new Date(),
        totalTrackDuration: Math.round(track.duration_ms / 1000),

        metadata: {
          sourceType: 'spotify',
          volume: device.volume_percent,
          audioQuality: 'spotify-quality',
          userAgent: 'Spotify Integration',
          deviceType: device.type
        },

        spotifyData: {
          trackId: track.id,
          trackUri: track.uri,
          playlistId: playbackState.context?.type === 'playlist' ?
            playbackState.context.uri.split(':')[2] : null,
          playlistUri: playbackState.context?.type === 'playlist' ?
            playbackState.context.uri : null,
          albumId: track.album.id,
          albumUri: track.album.uri,
          artistIds: track.artists.map(a => a.id),
          isrc: track.external_ids?.isrc,
          popularity: track.popularity,
          explicit: track.explicit,
          durationMs: track.duration_ms,
          previewUrl: track.preview_url,
          externalUrls: track.external_urls,
          availableMarkets: track.available_markets,
          discNumber: track.disc_number,
          trackNumber: track.track_number,
          playedFromContext: {
            type: playbackState.context?.type,
            uri: playbackState.context?.uri,
            href: playbackState.context?.href
          },
          playbackContext: {
            device: {
              id: device.id,
              name: device.name,
              type: device.type,
              volumePercent: device.volume_percent
            },
            shuffleState: playbackState.shuffle_state,
            repeatState: playbackState.repeat_state,
            progressMs: playbackState.progress_ms,
            isPlaying: playbackState.is_playing,
            currentlyPlayingType: playbackState.currently_playing_type
          }
        },

        auditInfo: {
          playbackSource: 'spotify',
          systemUsed: 'automated',
          triggerType: 'system_auto',
          isManualSelection: false,
          isScheduledPlayback: false,
          isAutomatedPlayback: true
        }
      });

      await playHistory.save();

      // Update user stats
      await User.findByIdAndUpdate(userId, {
        $inc: {
          'spotifyIntegration.stats.totalTracksPlayed': 1,
          'spotifyIntegration.stats.totalPlayTime': Math.round(track.duration_ms / 1000)
        },
        'spotifyIntegration.stats.lastPlayedTrack': {
          id: track.id,
          name: track.name,
          artist: track.artists.map(a => a.name).join(', '),
          playedAt: new Date()
        },
        'spotifyIntegration.lastSync': new Date()
      });

      return playHistory;
    } catch (error) {
      console.error('Failed to log Spotify track:', error);
      throw error;
    }
  }

  // Get recently played tracks from Spotify
  async getRecentlyPlayed(userId, limit = 50) {
    return await this.makeSpotifyRequest(userId, `/me/player/recently-played?limit=${limit}`);
  }

  // Sync recently played tracks for compliance
  async syncRecentlyPlayed(userId, storeId) {
    try {
      const recentTracks = await this.getRecentlyPlayed(userId);
      const syncedTracks = [];

      for (const item of recentTracks.items) {
        const track = item.track;
        const playedAt = new Date(item.played_at);

        // Check if already logged
        const existingLog = await PlayHistory.findOne({
          storeId: storeId,
          'spotifyData.trackId': track.id,
          startTime: {
            $gte: new Date(playedAt.getTime() - 30000),
            $lte: new Date(playedAt.getTime() + 30000)
          }
        });

        if (existingLog) continue;

        // Create track record if needed
        let trackRecord = await Track.findOne({ 'metadata.spotifyId': track.id });
        if (!trackRecord) {
          trackRecord = new Track({
            title: track.name,
            artist: track.artists.map(a => a.name).join(', '),
            album: track.album.name,
            duration: Math.round(track.duration_ms / 1000),
            metadata: {
              spotifyId: track.id,
              spotifyUri: track.uri,
              isrc: track.external_ids?.isrc,
              popularity: track.popularity,
              explicit: track.explicit
            },
            source: 'spotify',
            uploadedBy: userId
          });
          await trackRecord.save();
        }

        // Create play history
        const playHistory = new PlayHistory({
          storeId: storeId,
          deviceId: 'spotify-sync',
          trackId: trackRecord._id,
          startTime: playedAt,
          endTime: new Date(playedAt.getTime() + track.duration_ms),
          totalTrackDuration: Math.round(track.duration_ms / 1000),
          durationPlayed: Math.round(track.duration_ms / 1000),
          playbackStatus: 'completed',

          metadata: {
            sourceType: 'spotify'
          },

          spotifyData: {
            trackId: track.id,
            trackUri: track.uri,
            albumId: track.album.id,
            artistIds: track.artists.map(a => a.id),
            isrc: track.external_ids?.isrc,
            popularity: track.popularity,
            explicit: track.explicit,
            durationMs: track.duration_ms
          },

          auditInfo: {
            playbackSource: 'spotify',
            systemUsed: 'automated',
            triggerType: 'system_auto'
          }
        });

        await playHistory.save();
        syncedTracks.push(playHistory);
      }

      return syncedTracks;
    } catch (error) {
      console.error('Failed to sync recently played tracks:', error);
      throw error;
    }
  }

  // Disconnect Spotify integration
  async disconnectSpotify(userId) {
    try {
      await User.findByIdAndUpdate(userId, {
        'spotifyIntegration.isConnected': false,
        'spotifyIntegration.accessToken': null,
        'spotifyIntegration.refreshToken': null,
        'spotifyIntegration.tokenExpiry': null,
        'spotifyIntegration.lastSync': new Date()
      });

      return { success: true, message: 'Spotify disconnected successfully' };
    } catch (error) {
      console.error('Failed to disconnect Spotify:', error);
      throw error;
    }
  }

  // Get Spotify integration status
  async getIntegrationStatus(userId) {
    const user = await User.findById(userId).select('spotifyIntegration');
    if (!user) {
      throw new Error('User not found');
    }

    const integration = user.spotifyIntegration;
    return {
      isConnected: integration.isConnected,
      displayName: integration.displayName,
      email: integration.email,
      product: integration.product,
      connectionDate: integration.connectionDate,
      lastSync: integration.lastSync,
      stats: integration.stats,
      hasValidToken: integration.tokenExpiry && integration.tokenExpiry > new Date()
    };
  }
}

module.exports = new SpotifyService();
