const request = require('supertest');
const app = require('../server');
const User = require('../models/User.model');
const jwt = require('jsonwebtoken');

describe('Spotify Integration Tests', () => {
  let authToken;
  let testUser;

  beforeAll(async () => {
    // Create a test user
    testUser = new User({
      email: '<EMAIL>',
      password: 'password123',
      role: 'store',
      storeId: '507f1f77bcf86cd799439011',
      isVerified: true
    });
    await testUser.save();

    // Generate auth token
    authToken = jwt.sign(
      { id: testUser._id, role: testUser.role },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    // Clean up test user
    await User.findByIdAndDelete(testUser._id);
  });

  describe('GET /api/spotify/auth-url', () => {
    it('should generate Spotify authorization URL', async () => {
      const response = await request(app)
        .get('/api/spotify/auth-url')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('authUrl');
      expect(response.body.authUrl).toContain('accounts.spotify.com/authorize');
      expect(response.body.authUrl).toContain('client_id');
      expect(response.body.authUrl).toContain('redirect_uri');
      expect(response.body.authUrl).toContain('scope');
    });

    it('should include custom scopes when provided', async () => {
      const customScopes = 'user-read-playback-state,user-modify-playback-state';
      const response = await request(app)
        .get('/api/spotify/auth-url')
        .query({ scopes: customScopes })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.authUrl).toContain(encodeURIComponent(customScopes));
    });

    it('should require authentication', async () => {
      await request(app)
        .get('/api/spotify/auth-url')
        .expect(401);
    });
  });

  describe('POST /api/spotify/callback', () => {
    it('should handle OAuth callback with error', async () => {
      const response = await request(app)
        .post('/api/spotify/callback')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          error: 'access_denied',
          error_description: 'User denied access'
        })
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('access_denied');
    });

    it('should require authentication', async () => {
      await request(app)
        .post('/api/spotify/callback')
        .send({ code: 'test-code' })
        .expect(401);
    });

    // Note: Testing successful OAuth callback requires mocking Spotify API
    // which would be done in a more comprehensive test suite
  });

  describe('GET /api/spotify/status', () => {
    it('should return integration status for user without Spotify connection', async () => {
      const response = await request(app)
        .get('/api/spotify/status')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('isConnected', false);
      expect(response.body).toHaveProperty('hasValidToken', false);
    });

    it('should require authentication', async () => {
      await request(app)
        .get('/api/spotify/status')
        .expect(401);
    });
  });

  describe('GET /api/spotify/playback', () => {
    it('should return error when user is not connected to Spotify', async () => {
      const response = await request(app)
        .get('/api/spotify/playback')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('not connected');
    });

    it('should require authentication', async () => {
      await request(app)
        .get('/api/spotify/playback')
        .expect(401);
    });
  });

  describe('POST /api/spotify/playback/:action', () => {
    it('should return error when user is not connected to Spotify', async () => {
      const response = await request(app)
        .post('/api/spotify/playback/play')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('not connected');
    });

    it('should validate playback actions', async () => {
      const response = await request(app)
        .post('/api/spotify/playback/invalid-action')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });

    it('should require authentication', async () => {
      await request(app)
        .post('/api/spotify/playback/play')
        .expect(401);
    });
  });

  describe('POST /api/spotify/log-current', () => {
    it('should return error when user is not connected to Spotify', async () => {
      const response = await request(app)
        .post('/api/spotify/log-current')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('not connected');
    });

    it('should require authentication', async () => {
      await request(app)
        .post('/api/spotify/log-current')
        .expect(401);
    });
  });

  describe('POST /api/spotify/sync-recent', () => {
    it('should return error when user is not connected to Spotify', async () => {
      const response = await request(app)
        .post('/api/spotify/sync-recent')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('not connected');
    });

    it('should require authentication', async () => {
      await request(app)
        .post('/api/spotify/sync-recent')
        .expect(401);
    });
  });

  describe('POST /api/spotify/disconnect', () => {
    it('should successfully disconnect even when not connected', async () => {
      const response = await request(app)
        .post('/api/spotify/disconnect')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
    });

    it('should require authentication', async () => {
      await request(app)
        .post('/api/spotify/disconnect')
        .expect(401);
    });
  });
});

describe('Spotify Service Unit Tests', () => {
  const SpotifyService = require('../services/spotifyService');
  
  describe('Token Management', () => {
    it('should encrypt and decrypt tokens correctly', () => {
      const testToken = 'test-access-token-12345';
      const encrypted = SpotifyService.encryptToken(testToken);
      const decrypted = SpotifyService.decryptToken(encrypted);
      
      expect(decrypted).toBe(testToken);
      expect(encrypted).not.toBe(testToken);
    });

    it('should handle invalid encrypted tokens', () => {
      expect(() => {
        SpotifyService.decryptToken('invalid-encrypted-token');
      }).toThrow();
    });
  });

  describe('URL Generation', () => {
    it('should generate valid authorization URL', () => {
      const scopes = ['user-read-playback-state', 'user-modify-playback-state'];
      const authUrl = SpotifyService.generateAuthUrl(scopes);
      
      expect(authUrl).toContain('accounts.spotify.com/authorize');
      expect(authUrl).toContain('response_type=code');
      expect(authUrl).toContain('client_id');
      expect(authUrl).toContain('redirect_uri');
      expect(authUrl).toContain('scope');
    });
  });

  describe('Error Handling', () => {
    it('should handle Spotify API errors gracefully', async () => {
      // Mock a Spotify API error response
      const mockError = {
        response: {
          status: 401,
          data: {
            error: {
              status: 401,
              message: 'Invalid access token'
            }
          }
        }
      };

      const result = SpotifyService.handleSpotifyError(mockError);
      expect(result).toHaveProperty('error');
      expect(result.error).toContain('Invalid access token');
    });
  });
});

describe('Activity Logger Spotify Integration', () => {
  const activityLogger = require('../services/activityLogger');

  beforeEach(() => {
    activityLogger.setStoreId('507f1f77bcf86cd799439011');
  });

  it('should log Spotify playback start correctly', async () => {
    const mockTrackData = {
      id: 'spotify-track-123',
      uri: 'spotify:track:123',
      name: 'Test Song',
      artists: [{ id: 'artist-1', name: 'Test Artist', uri: 'spotify:artist:1' }],
      album: {
        id: 'album-1',
        name: 'Test Album',
        uri: 'spotify:album:1',
        images: [{ url: 'https://example.com/image.jpg' }]
      },
      duration_ms: 180000,
      popularity: 75,
      explicit: false,
      external_ids: { isrc: 'TEST123456789' },
      preview_url: 'https://example.com/preview.mp3'
    };

    const mockPlaybackData = {
      device: { id: 'device-1', name: 'Test Device', type: 'Computer', volume_percent: 50 },
      shuffle_state: false,
      repeat_state: 'off',
      progress_ms: 0,
      context: { type: 'playlist', uri: 'spotify:playlist:123' }
    };

    // Mock the logActivity method to capture the data
    const originalLogActivity = activityLogger.logActivity;
    let loggedActivity = null;
    activityLogger.logActivity = jest.fn().mockImplementation((activity) => {
      loggedActivity = activity;
      return Promise.resolve();
    });

    await activityLogger.logSpotifyStart(mockTrackData, mockPlaybackData);

    expect(loggedActivity).toBeTruthy();
    expect(loggedActivity.activityType).toBe('spotify_start');
    expect(loggedActivity.sourceTo).toBe('spotify');
    expect(loggedActivity.spotifyData.trackId).toBe('spotify-track-123');
    expect(loggedActivity.spotifyData.trackName).toBe('Test Song');
    expect(loggedActivity.spotifyData.isrc).toBe('TEST123456789');

    // Restore original method
    activityLogger.logActivity = originalLogActivity;
  });
});
