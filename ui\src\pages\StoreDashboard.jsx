import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { audioService } from '../services/audioService';
import { useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Box,
  AppBar,
  Toolbar,
  IconButton,
  Typography,
  useTheme,
  useMediaQuery
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import MaterialSidebar from '../components/MaterialSidebar';
import MaterialStoreDashboard from './MaterialStoreDashboard';
import MaterialPlaylistManager from '../components/MaterialPlaylistManager';
import MaterialHelp from '../components/MaterialHelp';
import MaterialScheduleManager from '../components/MaterialScheduleManager';
import MaterialStoreAnalytics from '../components/MaterialStoreAnalytics';
import MaterialRadioStations from '../components/MaterialRadioStations';
import MaterialMessages from '../components/MaterialMessages';
import MaterialStoreTrackLibrary from '../components/MaterialStoreTrackLibrary';
import SpotifyIntegration from '../components/SpotifyIntegration';

import MaterialAccountManagement from '../components/MaterialAccountManagement';
import StoreLicenseView from '../components/StoreLicenseView';
import PlaylistManager from '../components/PlaylistManager';
import MusicPlayer from '../components/MusicPlayer';

import ScheduleManager from '../components/ScheduleManager';
import DragDropPlaylistBuilder from '../components/DragDropPlaylistBuilder';

const StoreDashboard = () => {
  const { user } = useAuth();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'player');
  const [currentPlaylist, setCurrentPlaylist] = useState(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));

  useEffect(() => {
    if (user?.storeId) {
      audioService.setStoreId(user.storeId);
      // Sync any offline logs when coming online
      audioService.syncOfflineLogs();
    }
  }, [user]);

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  const handlePlaylistSelect = (playlist) => {
    setCurrentPlaylist(playlist);
    if (playlist?.tracks) {
      audioService.loadPlaylist(playlist.tracks);
    }
    setActiveTab('player');
  };

  // For the main player view, use the new Material UI design
  if (activeTab === 'player') {
    return <MaterialStoreDashboard />;
  }

  // For other tabs, use consistent Material UI layout
  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Sidebar */}
      <MaterialSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      {/* Main Content */}
      <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Mobile Header */}
        {isMobile && (
          <AppBar position="static" sx={{ bgcolor: 'background.paper' }}>
            <Toolbar>
              <IconButton
                edge="start"
                color="inherit"
                onClick={() => setSidebarOpen(true)}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                Store Dashboard
              </Typography>
            </Toolbar>
          </AppBar>
        )}

        {/* Content Area */}
        <Box sx={{ p: 3, flexGrow: 1 }}>
          {/* Desktop Header */}
          

          {/* Tab Content */}
          <Box sx={{ mt: 2 }}>
            {activeTab === 'playlists' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialPlaylistManager />
              </motion.div>
            )}

            {activeTab === 'library' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialStoreTrackLibrary />
              </motion.div>
            )}

            {activeTab === 'spotify' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <SpotifyIntegration />
              </motion.div>
            )}

            {activeTab === 'help' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialHelp />
              </motion.div>
            )}

            {activeTab === 'schedule' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialScheduleManager storeId={user?.storeId} />
              </motion.div>
            )}

            {activeTab === 'analytics' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialStoreAnalytics />
              </motion.div>
            )}

            {activeTab === 'radio' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialRadioStations />
              </motion.div>
            )}

            {activeTab === 'messages' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialMessages />
              </motion.div>
            )}



            {activeTab === 'licenses' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <StoreLicenseView />
              </motion.div>
            )}

            {activeTab === 'account' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialAccountManagement />
              </motion.div>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default StoreDashboard;
