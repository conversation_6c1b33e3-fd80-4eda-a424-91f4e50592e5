const ComplianceReport = require('../models/ComplianceReport.model');
const PlayHistory = require('../models/PlayHistory.model');
const Track = require('../models/Track.model');
const Store = require('../models/Store.model');
const User = require('../models/User.model');
const ComplianceAlert = require('../models/ComplianceAlert.model');
const ActivityLog = require('../models/ActivityLog.model');
const { format, startOfDay, endOfDay, subDays, subMonths } = require('date-fns');
const { generateCSVReport, generatePDFReport, generateExcelReport, generateXMLReport } = require('../services/report.service');
const mongoose = require('mongoose');
const { auditActions } = require('../middleware/audit.middleware');

// Import new services
const complianceValidationService = require('../services/complianceValidation.service');
const realTimeComplianceService = require('../services/realTimeCompliance.service');
const samroReportingService = require('../services/samroReporting.service');
const sampraReportingService = require('../services/sampraReporting.service');

// Get compliance dashboard overview
exports.getComplianceDashboard = async (req, res) => {
  try {
    const { organization } = req.user;
    const today = new Date();
    const lastWeek = subDays(today, 7);
    const lastMonth = subMonths(today, 1);

    // Debug: Check total PlayHistory records
    const totalPlayHistoryCount = await PlayHistory.countDocuments();
    console.log(`Total PlayHistory records in database: ${totalPlayHistoryCount}`);

    // Get recent reports
    const recentReports = await ComplianceReport.find({
      $or: [
        { organization: organization },
        { organization: 'All' },
        ...(req.user.role === 'compliance_admin' ? [{}] : [])
      ]
    })
    .sort({ createdAt: -1 })
    .limit(10)
    .populate('generatedBy', 'username organization')
    .populate('verifiedBy', 'username organization');

    // Get compliance statistics
    const totalReports = await ComplianceReport.countDocuments({
      createdAt: { $gte: lastMonth }
    });

    const pendingReports = await ComplianceReport.countDocuments({
      'compliance.verificationStatus': 'pending',
      createdAt: { $gte: lastMonth }
    });

    const verifiedReports = await ComplianceReport.countDocuments({
      'compliance.verificationStatus': 'verified',
      createdAt: { $gte: lastMonth }
    });

    // Get play statistics for the organization
    const playStats = await PlayHistory.aggregate([
      {
        $match: {
          playedDate: { $gte: lastWeek }
        }
      },
      {
        $group: {
          _id: null,
          totalPlays: { $sum: 1 },
          totalDuration: { $sum: '$durationPlayed' },
          uniqueTracks: { $addToSet: '$trackId' }
        }
      }
    ]);

    const stats = playStats[0] || { totalPlays: 0, totalDuration: 0, uniqueTracks: [] };

    res.json({
      overview: {
        totalReports,
        pendingReports,
        verifiedReports,
        complianceRate: totalReports > 0 ? ((verifiedReports / totalReports) * 100).toFixed(1) : 0
      },
      playStats: {
        totalPlays: stats.totalPlays,
        totalDuration: stats.totalDuration,
        uniqueTracks: stats.uniqueTracks.length,
        totalPlayHistoryRecords: totalPlayHistoryCount // Add this for debugging
      },
      recentReports
    });
  } catch (error) {
    console.error('Error fetching compliance dashboard:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard data' });
  }
};

// Generate compliance report
exports.generateComplianceReport = async (req, res) => {
  try {
    const { reportType, dateRange, storeIds, organization } = req.body;
    const { startDate, endDate } = dateRange;

    // Validate date range
    if (new Date(startDate) > new Date(endDate)) {
      return res.status(400).json({ error: 'Invalid date range' });
    }

    // Enforce organization restrictions based on user role
    let allowedOrganization;
    if (req.user.role === 'samro_staff') {
      allowedOrganization = 'SAMRO';
      if (organization && organization !== 'SAMRO') {
        return res.status(403).json({
          error: 'SAMRO staff can only generate reports for SAMRO organization'
        });
      }
    } else if (req.user.role === 'sampra_staff') {
      allowedOrganization = 'SAMPRA';
      if (organization && organization !== 'SAMPRA') {
        return res.status(403).json({
          error: 'SAMPRA staff can only generate reports for SAMPRA organization'
        });
      }
    } else if (req.user.role === 'compliance_admin' || req.user.role === 'admin') {
      allowedOrganization = organization || 'All';
    } else {
      allowedOrganization = req.user.organization || 'All';
    }

    console.log('Report generation authorization:', {
      userRole: req.user.role,
      userOrganization: req.user.organization,
      requestedOrganization: organization,
      allowedOrganization
    });

    // Get play history data
    const matchConditions = {
      playedDate: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };

    if (storeIds && storeIds.length > 0) {
      matchConditions.storeId = { $in: storeIds };
    }

    console.log('Compliance report query conditions:', matchConditions);

    const playData = await PlayHistory.find(matchConditions)
      .populate('trackId', 'title artist album genre duration isrcCode iswcCode composers publishers recordLabel compliance')
      .populate('storeId', 'name location')
      .sort({ playedDate: -1 });

    // Also get activity logs for Spotify plays
    const Activity = require('../models/Activity.model');
    const spotifyActivities = await Activity.find({
      ...matchConditions,
      activityType: { $in: ['spotify_start', 'spotify_control'] },
      'spotifyData.trackId': { $exists: true }
    }).sort({ timestamp: -1 });

    console.log(`Found ${playData.length} play history records and ${spotifyActivities.length} Spotify activities for compliance report`);

    // Get store information
    const stores = await Store.find(
      storeIds && storeIds.length > 0 ? { _id: { $in: storeIds } } : {}
    );

    // Process track data for compliance
    const trackDataMap = new Map();
    let totalPlays = 0;
    let totalDuration = 0;
    let spotifyPlays = 0;
    let spotifyDuration = 0;

    // Process regular play history
    playData.forEach(play => {
      if (!play.trackId) return;

      const trackKey = play.trackId._id.toString();
      totalPlays++;
      totalDuration += play.durationPlayed || 0;

      if (!trackDataMap.has(trackKey)) {
        trackDataMap.set(trackKey, {
          trackId: play.trackId._id,
          title: play.trackId.title,
          artist: play.trackId.artist,
          album: play.trackId.album,
          genre: play.trackId.genre,
          duration: play.trackId.duration,

          // Enhanced compliance metadata
          isrcCode: play.trackId.isrcCode,
          iswcCode: play.trackId.iswcCode,

          // Composer information
          composers: play.trackId.composers || [],

          // Publisher information
          publishers: play.trackId.publishers || [],

          // Record label information
          recordLabel: play.trackId.recordLabel || {},

          // Compliance status
          compliance: {
            samroRegistered: play.trackId.compliance?.samroRegistered || false,
            sampraRegistered: play.trackId.compliance?.sampraRegistered || false,
            risaCompliant: play.trackId.compliance?.risaCompliant || true,
            verificationStatus: play.trackId.compliance?.verificationStatus || 'pending'
          },

          // Playback statistics
          playCount: 0,
          totalDuration: 0,

          // Detailed playback logs for compliance
          playbackLogs: []
        });
      }

      const trackData = trackDataMap.get(trackKey);
      trackData.playCount++;
      trackData.totalDuration += play.durationPlayed || 0;

      // Add detailed playback log for compliance
      trackData.playbackLogs.push({
        playedAt: play.playedDate,
        startTime: play.startTime,
        endTime: play.endTime,
        durationPlayed: play.durationPlayed,
        completionPercentage: play.completionPercentage,
        playbackStatus: play.playbackStatus,
        storeId: play.storeId._id,
        storeName: play.storeId.name,
        location: play.location || {},
        deviceId: play.deviceId,
        sessionId: play.auditInfo?.sessionId,
        // New fields for SAMRO/SAMPRA compliance
        playbackSource: play.auditInfo?.playbackSource || play.metadata?.sourceType || 'music_player',
        systemUsed: play.auditInfo?.systemUsed || (play.auditInfo?.isManualSelection ? 'manual' : 'automated'),
        triggerType: play.auditInfo?.triggerType || (play.auditInfo?.isScheduledPlayback ? 'schedule_auto' : 'playlist_auto')
      });
    });

    // Process Spotify activity data
    spotifyActivities.forEach(activity => {
      if (!activity.spotifyData?.trackId) return;

      const trackKey = `spotify_${activity.spotifyData.trackId}`;
      spotifyPlays++;

      // Estimate duration from Spotify data
      const estimatedDuration = activity.spotifyData.duration || 0;
      spotifyDuration += estimatedDuration;

      if (!trackDataMap.has(trackKey)) {
        trackDataMap.set(trackKey, {
          trackId: `spotify_${activity.spotifyData.trackId}`,
          title: activity.spotifyData.trackName,
          artist: activity.spotifyData.artists?.map(a => a.name || a).join(', ') || 'Unknown Artist',
          album: activity.spotifyData.album?.name || 'Unknown Album',
          genre: 'Unknown', // Spotify doesn't provide genre in basic track data
          duration: activity.spotifyData.duration,

          // Spotify-specific compliance metadata
          isrcCode: activity.spotifyData.isrc,
          iswcCode: null, // Not available from Spotify API

          // Composer information (limited from Spotify)
          composers: [],

          // Publisher information (not available from Spotify API)
          publishers: [],

          // Record label information (not available from basic Spotify API)
          recordLabel: {},

          // Compliance status
          compliance: {
            samroStatus: activity.spotifyData.isrc ? 'compliant' : 'missing_isrc',
            sampraStatus: activity.spotifyData.isrc ? 'compliant' : 'missing_isrc',
            risaStatus: 'unknown',
            lastChecked: new Date(),
            issues: activity.spotifyData.isrc ? [] : ['Missing ISRC code']
          },

          playCount: 0,
          totalDuration: 0,
          source: 'spotify',

          // Detailed playback logs for compliance
          playbackLogs: []
        });
      }

      const trackData = trackDataMap.get(trackKey);
      trackData.playCount++;
      trackData.totalDuration += estimatedDuration;

      // Add detailed playback log for compliance
      trackData.playbackLogs.push({
        playedAt: activity.timestamp,
        startTime: activity.timestamp,
        endTime: new Date(activity.timestamp.getTime() + estimatedDuration),
        durationPlayed: estimatedDuration,
        completionPercentage: 100, // Assume full play for Spotify
        playbackStatus: 'completed',
        storeId: activity.storeId,
        storeName: 'Spotify Integration',
        location: {},
        deviceId: activity.spotifyData.device?.id || activity.deviceId,
        sessionId: activity.sessionId,
        // Spotify-specific fields
        playbackSource: 'spotify',
        systemUsed: 'spotify_integration',
        triggerType: activity.spotifyData.action || 'spotify_play',
        spotifyTrackId: activity.spotifyData.trackId,
        spotifyUri: activity.spotifyData.trackUri,
        spotifyDevice: activity.spotifyData.device?.name,
        spotifyContext: activity.spotifyData.playbackContext?.type
      });
    });

    const trackData = Array.from(trackDataMap.values());
    const uniqueArtists = new Set(trackData.map(t => t.artist)).size;

    // Generate unique report ID
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    const randomStr = Math.random().toString(36).substring(2, 8).toUpperCase();
    const reportId = `${allowedOrganization}-${dateStr}-${randomStr}`;

    console.log(`Generating compliance report with ${trackData.length} tracks and ${totalPlays} total plays for organization: ${allowedOrganization}`);

    // Create compliance report
    const complianceReport = new ComplianceReport({
      reportId,
      reportType,
      organization: allowedOrganization,
      dateRange: { startDate: new Date(startDate), endDate: new Date(endDate) },
      stores: stores.map(store => ({
        storeId: store._id,
        storeName: store.name,
        location: store.location
      })),
      trackData,
      summary: {
        totalTracks: trackData.length,
        totalPlays: totalPlays + spotifyPlays,
        totalDuration: totalDuration + spotifyDuration,
        uniqueArtists,
        spotifyIntegration: {
          spotifyPlays,
          spotifyDuration,
          spotifyTracks: trackData.filter(t => t.source === 'spotify').length,
          percentageOfTotal: totalPlays + spotifyPlays > 0 ?
            Math.round((spotifyPlays / (totalPlays + spotifyPlays)) * 100) : 0
        }
      },
      compliance: {
        risaCompliant: true,
        samroCompliant: true,
        sampraCompliant: true,
        issues: [],
        verificationStatus: 'pending'
      },
      generatedBy: req.user.id,
      status: 'draft'
    });

    await complianceReport.save();

    // Add audit trail entry to the report
    complianceReport.auditTrail.push({
      action: 'Report Generated',
      performedBy: req.user.id,
      details: `${reportType} report generated for ${allowedOrganization} organization by ${req.user.role}`
    });

    await complianceReport.save();

    // Log to comprehensive audit system
    try {
      await auditActions.reportGenerated(
        req.user.id,
        reportId,
        reportType,
        allowedOrganization,
        req.ip || req.connection.remoteAddress || 'unknown',
        req.get('User-Agent') || 'unknown'
      );
    } catch (auditError) {
      console.error('Failed to log report generation audit:', auditError);
      // Don't fail the request if audit logging fails
    }

    res.status(201).json({
      message: 'Compliance report generated successfully',
      report: complianceReport,
      reportId
    });
  } catch (error) {
    console.error('Error generating compliance report:', error);
    res.status(500).json({ error: 'Failed to generate compliance report' });
  }
};

// Get all compliance reports
exports.getComplianceReports = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, organization, reportType } = req.query;
    const skip = (page - 1) * limit;

    const filter = {};
    
    // Apply organization filter based on user role
    if (req.user.role === 'samro_staff') {
      filter.$or = [{ organization: 'SAMRO' }, { organization: 'All' }];
    } else if (req.user.role === 'sampra_staff') {
      filter.$or = [{ organization: 'SAMPRA' }, { organization: 'All' }];
    } else if (req.user.organization && req.user.role !== 'compliance_admin' && req.user.role !== 'admin') {
      filter.$or = [{ organization: req.user.organization }, { organization: 'All' }];
    }

    if (status) filter['compliance.verificationStatus'] = status;
    if (organization) filter.organization = organization;
    if (reportType) filter.reportType = reportType;

    const reports = await ComplianceReport.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('generatedBy', 'username organization')
      .populate('verifiedBy', 'username organization');

    const total = await ComplianceReport.countDocuments(filter);

    res.json({
      reports,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Error fetching compliance reports:', error);
    res.status(500).json({ error: 'Failed to fetch compliance reports' });
  }
};

// Get specific compliance report
exports.getComplianceReport = async (req, res) => {
  try {
    const { id } = req.params;
    
    const report = await ComplianceReport.findById(id)
      .populate('generatedBy', 'username organization')
      .populate('verifiedBy', 'username organization')
      .populate('auditTrail.performedBy', 'username organization');

    if (!report) {
      return res.status(404).json({ error: 'Report not found' });
    }

    // Check access permissions
    const hasAccess = 
      req.user.role === 'admin' ||
      req.user.role === 'compliance_admin' ||
      (req.user.role === 'samro_staff' && (report.organization === 'SAMRO' || report.organization === 'All')) ||
      (req.user.role === 'sampra_staff' && (report.organization === 'SAMPRA' || report.organization === 'All')) ||
      report.generatedBy.toString() === req.user.id;

    if (!hasAccess) {
      return res.status(403).json({ error: 'Access denied to this report' });
    }

    res.json(report);
  } catch (error) {
    console.error('Error fetching compliance report:', error);
    res.status(500).json({ error: 'Failed to fetch compliance report' });
  }
};

// Verify compliance report
exports.verifyComplianceReport = async (req, res) => {
  try {
    const { id } = req.params;
    const { verificationStatus, issues } = req.body;

    const report = await ComplianceReport.findById(id);
    if (!report) {
      return res.status(404).json({ error: 'Report not found' });
    }

    // Update compliance status
    report.compliance.verificationStatus = verificationStatus;
    if (issues) {
      report.compliance.issues = issues;
    }
    report.verifiedBy = req.user.id;

    // Add audit trail entry
    report.auditTrail.push({
      action: 'Report Verified',
      performedBy: req.user.id,
      details: `Report verification status changed to ${verificationStatus}`
    });

    await report.save();

    res.json({
      message: 'Report verification updated successfully',
      report
    });
  } catch (error) {
    console.error('Error verifying compliance report:', error);
    res.status(500).json({ error: 'Failed to verify compliance report' });
  }
};

// Export compliance report
exports.exportComplianceReport = async (req, res) => {
  try {
    const { id } = req.params;
    const { format = 'csv' } = req.query;

    const report = await ComplianceReport.findById(id);
    if (!report) {
      return res.status(404).json({ error: 'Report not found' });
    }

    let filePath;

    // Generate meaningful filename with store names, organization, and date
    const dateStr = new Date(report.dateRange.startDate).toISOString().slice(0, 10);
    const endDateStr = new Date(report.dateRange.endDate).toISOString().slice(0, 10);
    const storeNames = report.stores.length > 0
      ? report.stores.slice(0, 2).map(s => s.storeName.replace(/[^a-zA-Z0-9]/g, '_')).join('_')
      : 'AllStores';
    const truncatedStoreNames = storeNames.length > 30 ? storeNames.substring(0, 30) : storeNames;

    const filename = `${report.organization}_${report.reportType}_${truncatedStoreNames}_${dateStr}_to_${endDateStr}`;

    if (format === 'csv') {
      filePath = await generateCSVReport(report.trackData, filename);
    } else if (format === 'pdf') {
      filePath = await generatePDFReport(report.trackData, filename);
    } else if (format === 'excel') {
      filePath = await generateExcelReport(report.trackData, filename);
    } else if (format === 'xml') {
      filePath = await generateXMLReport(report.trackData, filename);
    } else {
      return res.status(400).json({ error: 'Unsupported export format. Supported formats: csv, pdf, excel, xml' });
    }

    // Update report with export information
    report.exportedFormats.push({
      format,
      filePath,
      exportedAt: new Date(),
      exportedBy: req.user.id
    });

    // Add audit trail entry
    report.auditTrail.push({
      action: 'Report Exported',
      performedBy: req.user.id,
      details: `Report exported in ${format.toUpperCase()} format`
    });

    await report.save();

    // For Excel format, return the file directly as blob
    if (format === 'excel') {
      const fs = require('fs');
      const fullFilePath = require('path').join(__dirname, '../', filePath);

      try {
        // Check if file exists
        if (!fs.existsSync(fullFilePath)) {
          return res.status(404).json({ error: 'Excel file not found' });
        }

        // Read the file and send as blob
        const fileBuffer = fs.readFileSync(fullFilePath);

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}.xlsx"`);
        res.setHeader('Content-Length', fileBuffer.length);

        return res.send(fileBuffer);
      } catch (fileError) {
        console.error('Excel file read error:', fileError);
        return res.status(500).json({ error: 'Failed to read Excel file' });
      }
    }

    // For other formats, return JSON with download URL
    // Normalize file path to use forward slashes
    const normalizedPath = filePath.replace(/\\/g, '/');
    const downloadFilename = normalizedPath.split('/').pop();
    
    // Ensure the download URL uses the correct path format
    const downloadUrl = `/api/reports/${downloadFilename}`;
    
    console.log('Generated download URL:', downloadUrl);
    res.json({
      message: 'Report exported successfully',
      downloadUrl: downloadUrl
    });
  } catch (error) {
    console.error('Error exporting compliance report:', error);
    res.status(500).json({ error: 'Failed to export compliance report' });
  }
};

// Bulk export compliance reports
exports.bulkExportReports = async (req, res) => {
  try {
    const { reportIds, formats } = req.body;

    if (!reportIds || !Array.isArray(reportIds) || reportIds.length === 0) {
      return res.status(400).json({ error: 'Report IDs are required' });
    }

    if (!formats || !Array.isArray(formats) || formats.length === 0) {
      return res.status(400).json({ error: 'Export formats are required' });
    }

    const results = [];
    const errors = [];

    // Process each report and format combination
    for (const reportId of reportIds) {
      try {
        const report = await ComplianceReport.findById(reportId);
        if (!report) {
          errors.push({ reportId, error: 'Report not found' });
          continue;
        }

        for (const format of formats) {
          try {
            let filePath;

            // Generate meaningful filename for bulk export
            const dateStr = new Date(report.dateRange.startDate).toISOString().slice(0, 10);
            const endDateStr = new Date(report.dateRange.endDate).toISOString().slice(0, 10);
            const storeNames = report.stores.length > 0
              ? report.stores.slice(0, 2).map(s => s.storeName.replace(/[^a-zA-Z0-9]/g, '_')).join('_')
              : 'AllStores';
            const truncatedStoreNames = storeNames.length > 30 ? storeNames.substring(0, 30) : storeNames;

            const filename = `BULK_${report.organization}_${report.reportType}_${truncatedStoreNames}_${dateStr}_to_${endDateStr}`;

            if (format === 'csv') {
              filePath = await generateCSVReport(report.trackData, filename);
            } else if (format === 'pdf') {
              filePath = await generatePDFReport(report.trackData, filename);
            } else if (format === 'excel') {
              filePath = await generateExcelReport(report.trackData, filename);
            } else if (format === 'xml') {
              filePath = await generateXMLReport(report.trackData, filename);
            } else {
              errors.push({ reportId, format, error: 'Unsupported format' });
              continue;
            }

            // Update report with export information
            report.exportedFormats.push({
              format,
              filePath,
              exportedAt: new Date(),
              exportedBy: req.user.id
            });

            // Add audit trail entry
            report.auditTrail.push({
              action: 'Bulk Export',
              performedBy: req.user.id,
              details: `Report exported in ${format.toUpperCase()} format via bulk export`
            });

            await report.save();

            // Fix URL construction - filePath already includes 'reports/' prefix
            const downloadFilename = filePath.split('/').pop();
            results.push({
              reportId,
              format,
              filename: downloadFilename,
              downloadUrl: `/reports/${downloadFilename}`
            });
          } catch (formatError) {
            console.error(`Error exporting ${reportId} in ${format}:`, formatError);
            errors.push({ reportId, format, error: formatError.message });
          }
        }
      } catch (reportError) {
        console.error(`Error processing report ${reportId}:`, reportError);
        errors.push({ reportId, error: reportError.message });
      }
    }

    res.json({
      message: 'Bulk export completed',
      results,
      errors,
      summary: {
        totalRequested: reportIds.length * formats.length,
        successful: results.length,
        failed: errors.length
      }
    });
  } catch (error) {
    console.error('Error in bulk export:', error);
    res.status(500).json({ error: 'Failed to perform bulk export' });
  }
};

// Get real-time monitoring data
exports.getRealTimeMonitoring = async (req, res) => {
  try {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Debug: Check total PlayHistory records
    const totalPlayHistoryCount = await PlayHistory.countDocuments();
    console.log(`Total PlayHistory records: ${totalPlayHistoryCount}`);

    // Get recent plays (expand time range if no recent data)
    let recentPlays = await PlayHistory.find({
      playedDate: { $gte: oneHourAgo }
    })
    .populate('trackId', 'title artist')
    .populate('storeId', 'name')
    .sort({ playedDate: -1 })
    .limit(50);

    // If no recent plays in last hour, try last 24 hours
    if (recentPlays.length === 0) {
      console.log('No plays in last hour, checking last 24 hours...');
      recentPlays = await PlayHistory.find({
        playedDate: { $gte: oneDayAgo }
      })
      .populate('trackId', 'title artist')
      .populate('storeId', 'name')
      .sort({ playedDate: -1 })
      .limit(50);
    }

    // Get active stores (also expand time range)
    let activeStores = await PlayHistory.aggregate([
      {
        $match: {
          playedDate: { $gte: oneHourAgo }
        }
      },
      {
        $group: {
          _id: '$storeId',
          lastActivity: { $max: '$playedDate' },
          playCount: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'stores',
          localField: '_id',
          foreignField: '_id',
          as: 'store'
        }
      }
    ]);

    // If no active stores in last hour, try last 24 hours
    if (activeStores.length === 0) {
      console.log('No active stores in last hour, checking last 24 hours...');
      activeStores = await PlayHistory.aggregate([
        {
          $match: {
            playedDate: { $gte: oneDayAgo }
          }
        },
        {
          $group: {
            _id: '$storeId',
            lastActivity: { $max: '$playedDate' },
            playCount: { $sum: 1 }
          }
        },
        {
          $lookup: {
            from: 'stores',
            localField: '_id',
            foreignField: '_id',
            as: 'store'
          }
        }
      ]);
    }

    console.log(`Found ${recentPlays.length} recent plays and ${activeStores.length} active stores`);

    res.json({
      recentPlays,
      activeStores,
      timestamp: now,
      debug: {
        totalPlayHistoryRecords: totalPlayHistoryCount,
        timeRange: totalPlayHistoryCount === 0 ? 'no_data' : (recentPlays.length > 0 ? 'last_hour' : 'last_24_hours')
      }
    });
  } catch (error) {
    console.error('Error fetching real-time monitoring data:', error);
    res.status(500).json({ error: 'Failed to fetch monitoring data' });
  }
};

// Get available filter options
exports.getAvailableStores = async (req, res) => {
  try {
    const stores = await Store.find({}, 'name location').sort({ name: 1 });
    res.json(stores);
  } catch (error) {
    console.error('Error fetching available stores:', error);
    res.status(500).json({ error: 'Failed to fetch stores' });
  }
};

exports.getAvailableArtists = async (req, res) => {
  try {
    const artists = await Track.distinct('artist').sort();
    res.json(artists.filter(artist => artist)); // Remove null/empty values
  } catch (error) {
    console.error('Error fetching available artists:', error);
    res.status(500).json({ error: 'Failed to fetch artists' });
  }
};

exports.getAvailableComposers = async (req, res) => {
  try {
    const composers = await Track.aggregate([
      { $unwind: '$composers' },
      { $group: { _id: '$composers.name' } },
      { $sort: { _id: 1 } }
    ]);
    res.json(composers.map(c => c._id).filter(name => name));
  } catch (error) {
    console.error('Error fetching available composers:', error);
    res.status(500).json({ error: 'Failed to fetch composers' });
  }
};

exports.getAvailablePublishers = async (req, res) => {
  try {
    const publishers = await Track.aggregate([
      { $unwind: '$publishers' },
      { $group: { _id: '$publishers.name' } },
      { $sort: { _id: 1 } }
    ]);
    res.json(publishers.map(p => p._id).filter(name => name));
  } catch (error) {
    console.error('Error fetching available publishers:', error);
    res.status(500).json({ error: 'Failed to fetch publishers' });
  }
};

exports.getAvailableRecordLabels = async (req, res) => {
  try {
    const labels = await Track.distinct('recordLabel.name').sort();
    res.json(labels.filter(label => label));
  } catch (error) {
    console.error('Error fetching available record labels:', error);
    res.status(500).json({ error: 'Failed to fetch record labels' });
  }
};

// Enhanced Analytics endpoints
exports.getTopTracks = async (req, res) => {
  try {
    const { startDate, endDate, limit = 50, organization, storeId, genre } = req.query;

    // Enforce organization restrictions based on user role
    let allowedOrganization;
    if (req.user.role === 'samro_staff') {
      allowedOrganization = 'SAMRO';
    } else if (req.user.role === 'sampra_staff') {
      allowedOrganization = 'SAMPRA';
    } else if (req.user.role === 'compliance_admin' || req.user.role === 'admin') {
      allowedOrganization = organization; // Can view any organization
    } else {
      allowedOrganization = req.user.organization;
    }

    // Debug: Check total PlayHistory records
    const totalPlayHistoryCount = await PlayHistory.countDocuments();
    console.log(`getTopTracks: Total PlayHistory records: ${totalPlayHistoryCount}, User role: ${req.user.role}, Allowed org: ${allowedOrganization}`);

    const matchConditions = {};
    if (startDate && endDate) {
      matchConditions.playedDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    // Add store filter if specified
    if (storeId && storeId !== 'all') {
      matchConditions.storeId = new mongoose.Types.ObjectId(storeId);
    }

    // Add organization-based filtering if needed
    // Note: This would require PlayHistory to have organization field or join with tracks that have organization info
    // For now, we'll filter at the track level if tracks have organization metadata

    const topTracks = await PlayHistory.aggregate([
      { $match: matchConditions },
      {
        $group: {
          _id: '$trackId',
          playCount: { $sum: 1 },
          totalDuration: { $sum: '$durationPlayed' },
          uniqueStores: { $addToSet: '$storeId' }
        }
      },
      {
        $lookup: {
          from: 'tracks',
          localField: '_id',
          foreignField: '_id',
          as: 'track'
        }
      },
      { $unwind: '$track' },
      {
        $addFields: {
          // Add organization-based filtering
          organizationMatch: {
            $cond: [
              { $eq: [allowedOrganization, 'SAMRO'] },
              '$track.compliance.samroRegistered',
              {
                $cond: [
                  { $eq: [allowedOrganization, 'SAMPRA'] },
                  '$track.compliance.sampraRegistered',
                  true
                ]
              }
            ]
          }
        }
      },
      // Filter by organization if specified
      ...(allowedOrganization && allowedOrganization !== 'All' ? [
        { $match: { organizationMatch: true } }
      ] : []),
      // Add genre filter if specified
      ...(genre && genre !== 'all' ? [
        { $match: { 'track.genre': genre } }
      ] : []),
      {
        $project: {
          title: '$track.title',
          artist: '$track.artist',
          album: '$track.album',
          genre: '$track.genre',
          duration: '$track.duration',
          isrcCode: '$track.isrcCode',
          iswcCode: '$track.iswcCode',
          playCount: 1,
          totalDuration: 1,
          uniqueStores: { $size: '$uniqueStores' },
          compliance: '$track.compliance',
          avgDurationPerPlay: {
            $cond: [
              { $gt: ['$playCount', 0] },
              { $divide: ['$totalDuration', '$playCount'] },
              0
            ]
          }
        }
      },
      { $sort: { playCount: -1 } },
      { $limit: parseInt(limit) }
    ]);

    console.log(`getTopTracks: Found ${topTracks.length} top tracks for organization: ${allowedOrganization}`);

    res.json({
      success: true,
      data: topTracks,
      metadata: {
        total: topTracks.length,
        totalPlayHistoryRecords: totalPlayHistoryCount,
        filters: { startDate, endDate, limit, storeId, genre, organization: allowedOrganization },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching top tracks:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch top tracks',
      details: error.message
    });
  }
};

exports.getUsageByRightsHolder = async (req, res) => {
  try {
    const { startDate, endDate, type = 'composer', organization, limit = 100 } = req.query;

    // Enforce organization restrictions
    let allowedOrganization;
    if (req.user.role === 'samro_staff') {
      allowedOrganization = 'SAMRO';
    } else if (req.user.role === 'sampra_staff') {
      allowedOrganization = 'SAMPRA';
    } else if (req.user.role === 'compliance_admin' || req.user.role === 'admin') {
      allowedOrganization = organization;
    } else {
      allowedOrganization = req.user.organization;
    }

    const matchConditions = {};
    if (startDate && endDate) {
      matchConditions.playedDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    let pipeline = [
      { $match: matchConditions },
      {
        $lookup: {
          from: 'tracks',
          localField: 'trackId',
          foreignField: '_id',
          as: 'track'
        }
      },
      { $unwind: '$track' }
    ];

    // Add organization filtering
    if (allowedOrganization && allowedOrganization !== 'All') {
      const orgFilter = {};
      if (allowedOrganization === 'SAMRO') {
        orgFilter['track.compliance.samroRegistered'] = true;
      } else if (allowedOrganization === 'SAMPRA') {
        orgFilter['track.compliance.sampraRegistered'] = true;
      }
      if (Object.keys(orgFilter).length > 0) {
        pipeline.push({ $match: orgFilter });
      }
    }

    if (type === 'composer') {
      pipeline.push(
        { $unwind: '$track.composers' },
        {
          $group: {
            _id: '$track.composers.name',
            playCount: { $sum: 1 },
            totalDuration: { $sum: '$durationPlayed' },
            uniqueTracks: { $addToSet: '$trackId' },
            ipiNumber: { $first: '$track.composers.ipiNumber' },
            role: { $first: '$track.composers.role' },
            share: { $avg: '$track.composers.share' }
          }
        }
      );
    } else if (type === 'publisher') {
      pipeline.push(
        { $unwind: '$track.publishers' },
        {
          $group: {
            _id: '$track.publishers.name',
            playCount: { $sum: 1 },
            totalDuration: { $sum: '$durationPlayed' },
            uniqueTracks: { $addToSet: '$trackId' },
            publisherCode: { $first: '$track.publishers.publisherCode' },
            territory: { $first: '$track.publishers.territory' },
            share: { $avg: '$track.publishers.share' }
          }
        }
      );
    } else if (type === 'artist') {
      pipeline.push({
        $group: {
          _id: '$track.artist',
          playCount: { $sum: 1 },
          totalDuration: { $sum: '$durationPlayed' },
          uniqueTracks: { $addToSet: '$trackId' },
          genres: { $addToSet: '$track.genre' }
        }
      });
    }

    pipeline.push(
      {
        $project: {
          name: '$_id',
          playCount: 1,
          totalDuration: 1,
          uniqueTracks: { $size: '$uniqueTracks' },
          ...(type === 'composer' && {
            ipiNumber: 1,
            role: 1,
            avgShare: { $round: ['$share', 2] }
          }),
          ...(type === 'publisher' && {
            publisherCode: 1,
            territory: 1,
            avgShare: { $round: ['$share', 2] }
          }),
          ...(type === 'artist' && {
            genres: 1,
            genreCount: { $size: '$genres' }
          }),
          avgDurationPerPlay: {
            $cond: [
              { $gt: ['$playCount', 0] },
              { $round: [{ $divide: ['$totalDuration', '$playCount'] }, 2] },
              0
            ]
          }
        }
      },
      { $sort: { playCount: -1 } },
      { $limit: parseInt(limit) }
    );

    const usage = await PlayHistory.aggregate(pipeline);

    res.json({
      success: true,
      data: usage,
      metadata: {
        type,
        organization: allowedOrganization,
        total: usage.length,
        filters: { startDate, endDate, type, organization: allowedOrganization },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching usage by rights holder:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch usage data',
      details: error.message
    });
  }
};

exports.getGeographicalData = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const matchConditions = {};
    if (startDate && endDate) {
      matchConditions.playedDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    const geoData = await PlayHistory.aggregate([
      { $match: matchConditions },
      {
        $lookup: {
          from: 'stores',
          localField: 'storeId',
          foreignField: '_id',
          as: 'store'
        }
      },
      { $unwind: '$store' },
      {
        $lookup: {
          from: 'tracks',
          localField: 'trackId',
          foreignField: '_id',
          as: 'track'
        }
      },
      { $unwind: '$track' },
      {
        $group: {
          _id: {
            city: '$store.city',
            province: '$store.province'
          },
          playCount: { $sum: 1 },
          totalDuration: { $sum: '$durationPlayed' },
          uniqueTracks: { $addToSet: '$trackId' },
          uniqueStores: { $addToSet: '$storeId' },
          compliantTracks: {
            $sum: {
              $cond: [
                { $and: [
                  { $eq: ['$track.compliance.risaCompliant', true] },
                  { $eq: ['$track.compliance.samroRegistered', true] },
                  { $eq: ['$track.compliance.sampraRegistered', true] }
                ]},
                1,
                0
              ]
            }
          }
        }
      },
      {
        $project: {
          city: '$_id.city',
          province: '$_id.province',
          playCount: 1,
          totalDuration: 1,
          uniqueTracks: { $size: '$uniqueTracks' },
          uniqueStores: { $size: '$uniqueStores' },
          complianceRate: {
            $cond: [
              { $gt: ['$playCount', 0] },
              { $multiply: [{ $divide: ['$compliantTracks', '$playCount'] }, 100] },
              100
            ]
          }
        }
      },
      { $sort: { playCount: -1 } }
    ]);

    res.json(geoData);
  } catch (error) {
    console.error('Error fetching geographical data:', error);
    res.status(500).json({ error: 'Failed to fetch geographical data' });
  }
};

exports.getComplianceOverview = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Enforce organization restrictions based on user role
    let allowedOrganizations = [];
    if (req.user.role === 'samro_staff') {
      allowedOrganizations = ['SAMRO'];
    } else if (req.user.role === 'sampra_staff') {
      allowedOrganizations = ['SAMPRA'];
    } else if (req.user.role === 'compliance_admin' || req.user.role === 'admin') {
      allowedOrganizations = null; // Can view all organizations
    } else {
      allowedOrganizations = [req.user.organization || 'All'];
    }

    console.log(`getComplianceOverview: User role: ${req.user.role}, Allowed orgs: ${allowedOrganizations}`);

    const matchConditions = {};
    if (startDate && endDate) {
      matchConditions.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    // Add organization filtering if user has restrictions
    if (allowedOrganizations && allowedOrganizations.length > 0) {
      matchConditions.organization = { $in: allowedOrganizations };
    }

    const overview = await ComplianceReport.aggregate([
      { $match: matchConditions },
      {
        $group: {
          _id: '$organization',
          totalReports: { $sum: 1 },
          verifiedReports: {
            $sum: {
              $cond: [{ $eq: ['$compliance.verificationStatus', 'verified'] }, 1, 0]
            }
          },
          pendingReports: {
            $sum: {
              $cond: [{ $eq: ['$compliance.verificationStatus', 'pending'] }, 1, 0]
            }
          },
          totalTracks: { $sum: '$summary.totalTracks' },
          totalPlays: { $sum: '$summary.totalPlays' }
        }
      },
      {
        $project: {
          organization: '$_id',
          totalReports: 1,
          verifiedReports: 1,
          pendingReports: 1,
          totalTracks: 1,
          totalPlays: 1,
          complianceRate: {
            $cond: [
              { $gt: ['$totalReports', 0] },
              { $multiply: [{ $divide: ['$verifiedReports', '$totalReports'] }, 100] },
              0
            ]
          }
        }
      }
    ]);

    res.json(overview);
  } catch (error) {
    console.error('Error fetching compliance overview:', error);
    res.status(500).json({ error: 'Failed to fetch compliance overview' });
  }
};

// New enhanced analytics endpoints
exports.getPlaysByTimeRange = async (req, res) => {
  try {
    const { startDate, endDate, granularity = 'day', organization, storeId } = req.query;

    // Enforce organization restrictions
    let allowedOrganization;
    if (req.user.role === 'samro_staff') {
      allowedOrganization = 'SAMRO';
    } else if (req.user.role === 'sampra_staff') {
      allowedOrganization = 'SAMPRA';
    } else if (req.user.role === 'compliance_admin' || req.user.role === 'admin') {
      allowedOrganization = organization;
    } else {
      allowedOrganization = req.user.organization;
    }

    const matchConditions = {};
    if (startDate && endDate) {
      matchConditions.playedDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    if (storeId && storeId !== 'all') {
      matchConditions.storeId = new mongoose.Types.ObjectId(storeId);
    }

    // Determine date grouping format based on granularity
    let dateFormat;
    switch (granularity) {
      case 'hour':
        dateFormat = '%Y-%m-%d %H:00:00';
        break;
      case 'day':
        dateFormat = '%Y-%m-%d';
        break;
      case 'week':
        dateFormat = '%Y-W%U';
        break;
      case 'month':
        dateFormat = '%Y-%m';
        break;
      default:
        dateFormat = '%Y-%m-%d';
    }

    let pipeline = [
      { $match: matchConditions },
      {
        $lookup: {
          from: 'tracks',
          localField: 'trackId',
          foreignField: '_id',
          as: 'track'
        }
      },
      { $unwind: '$track' }
    ];

    // Add organization filtering
    if (allowedOrganization && allowedOrganization !== 'All') {
      const orgFilter = {};
      if (allowedOrganization === 'SAMRO') {
        orgFilter['track.compliance.samroRegistered'] = true;
      } else if (allowedOrganization === 'SAMPRA') {
        orgFilter['track.compliance.sampraRegistered'] = true;
      }
      if (Object.keys(orgFilter).length > 0) {
        pipeline.push({ $match: orgFilter });
      }
    }

    pipeline.push(
      {
        $group: {
          _id: {
            $dateToString: {
              format: dateFormat,
              date: '$playedDate'
            }
          },
          playCount: { $sum: 1 },
          totalDuration: { $sum: '$durationPlayed' },
          uniqueTracks: { $addToSet: '$trackId' },
          uniqueStores: { $addToSet: '$storeId' },
          avgCompletionRate: { $avg: '$completionPercentage' }
        }
      },
      {
        $project: {
          date: '$_id',
          playCount: 1,
          totalDuration: 1,
          uniqueTracks: { $size: '$uniqueTracks' },
          uniqueStores: { $size: '$uniqueStores' },
          avgCompletionRate: { $round: ['$avgCompletionRate', 2] },
          avgDurationPerPlay: {
            $cond: [
              { $gt: ['$playCount', 0] },
              { $round: [{ $divide: ['$totalDuration', '$playCount'] }, 2] },
              0
            ]
          }
        }
      },
      { $sort: { date: 1 } }
    );

    const timeRangeData = await PlayHistory.aggregate(pipeline);

    res.json({
      success: true,
      data: timeRangeData,
      metadata: {
        granularity,
        organization: allowedOrganization,
        total: timeRangeData.length,
        filters: { startDate, endDate, granularity, storeId, organization: allowedOrganization },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching plays by time range:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch plays by time range',
      details: error.message
    });
  }
};

// Get genre analytics
exports.getGenreAnalytics = async (req, res) => {
  try {
    const { startDate, endDate, organization, limit = 20 } = req.query;

    // Enforce organization restrictions
    let allowedOrganization;
    if (req.user.role === 'samro_staff') {
      allowedOrganization = 'SAMRO';
    } else if (req.user.role === 'sampra_staff') {
      allowedOrganization = 'SAMPRA';
    } else if (req.user.role === 'compliance_admin' || req.user.role === 'admin') {
      allowedOrganization = organization;
    } else {
      allowedOrganization = req.user.organization;
    }

    const matchConditions = {};
    if (startDate && endDate) {
      matchConditions.playedDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    let pipeline = [
      { $match: matchConditions },
      {
        $lookup: {
          from: 'tracks',
          localField: 'trackId',
          foreignField: '_id',
          as: 'track'
        }
      },
      { $unwind: '$track' }
    ];

    // Add organization filtering
    if (allowedOrganization && allowedOrganization !== 'All') {
      const orgFilter = {};
      if (allowedOrganization === 'SAMRO') {
        orgFilter['track.compliance.samroRegistered'] = true;
      } else if (allowedOrganization === 'SAMPRA') {
        orgFilter['track.compliance.sampraRegistered'] = true;
      }
      if (Object.keys(orgFilter).length > 0) {
        pipeline.push({ $match: orgFilter });
      }
    }

    pipeline.push(
      {
        $group: {
          _id: { $ifNull: ['$track.genre', 'Unknown'] },
          playCount: { $sum: 1 },
          totalDuration: { $sum: '$durationPlayed' },
          uniqueTracks: { $addToSet: '$trackId' },
          uniqueArtists: { $addToSet: '$track.artist' },
          avgCompletionRate: { $avg: '$completionPercentage' }
        }
      },
      {
        $project: {
          genre: '$_id',
          playCount: 1,
          totalDuration: 1,
          uniqueTracks: { $size: '$uniqueTracks' },
          uniqueArtists: { $size: '$uniqueArtists' },
          avgCompletionRate: { $round: ['$avgCompletionRate', 2] },
          avgDurationPerPlay: {
            $cond: [
              { $gt: ['$playCount', 0] },
              { $round: [{ $divide: ['$totalDuration', '$playCount'] }, 2] },
              0
            ]
          }
        }
      },
      { $sort: { playCount: -1 } },
      { $limit: parseInt(limit) }
    );

    const genreData = await PlayHistory.aggregate(pipeline);

    // Calculate percentages
    const totalPlays = genreData.reduce((sum, genre) => sum + genre.playCount, 0);
    const genreDataWithPercentages = genreData.map(genre => ({
      ...genre,
      percentage: totalPlays > 0 ? ((genre.playCount / totalPlays) * 100).toFixed(2) : 0
    }));

    res.json({
      success: true,
      data: genreDataWithPercentages,
      metadata: {
        organization: allowedOrganization,
        total: genreDataWithPercentages.length,
        totalPlays,
        filters: { startDate, endDate, organization: allowedOrganization },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching genre analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch genre analytics',
      details: error.message
    });
  }
};

// Get compliance metrics
exports.getComplianceMetrics = async (req, res) => {
  try {
    const { startDate, endDate, organization, storeId } = req.query;

    // Enforce organization restrictions
    let allowedOrganization;
    if (req.user.role === 'samro_staff') {
      allowedOrganization = 'SAMRO';
    } else if (req.user.role === 'sampra_staff') {
      allowedOrganization = 'SAMPRA';
    } else if (req.user.role === 'compliance_admin' || req.user.role === 'admin') {
      allowedOrganization = organization;
    } else {
      allowedOrganization = req.user.organization;
    }

    const matchConditions = {};
    if (startDate && endDate) {
      matchConditions.playedDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    if (storeId && storeId !== 'all') {
      matchConditions.storeId = new mongoose.Types.ObjectId(storeId);
    }

    const pipeline = [
      { $match: matchConditions },
      {
        $lookup: {
          from: 'tracks',
          localField: 'trackId',
          foreignField: '_id',
          as: 'track'
        }
      },
      { $unwind: '$track' },
      {
        $group: {
          _id: null,
          totalPlays: { $sum: 1 },
          totalTracks: { $addToSet: '$trackId' },
          samroCompliantPlays: {
            $sum: {
              $cond: [{ $eq: ['$track.compliance.samroRegistered', true] }, 1, 0]
            }
          },
          sampraCompliantPlays: {
            $sum: {
              $cond: [{ $eq: ['$track.compliance.sampraRegistered', true] }, 1, 0]
            }
          },
          risaCompliantPlays: {
            $sum: {
              $cond: [{ $eq: ['$track.compliance.risaCompliant', true] }, 1, 0]
            }
          },
          fullyCompliantPlays: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$track.compliance.samroRegistered', true] },
                    { $eq: ['$track.compliance.sampraRegistered', true] },
                    { $eq: ['$track.compliance.risaCompliant', true] }
                  ]
                },
                1,
                0
              ]
            }
          }
        }
      },
      {
        $project: {
          totalPlays: 1,
          totalTracks: { $size: '$totalTracks' },
          samroComplianceRate: {
            $cond: [
              { $gt: ['$totalPlays', 0] },
              { $round: [{ $multiply: [{ $divide: ['$samroCompliantPlays', '$totalPlays'] }, 100] }, 2] },
              0
            ]
          },
          sampraComplianceRate: {
            $cond: [
              { $gt: ['$totalPlays', 0] },
              { $round: [{ $multiply: [{ $divide: ['$sampraCompliantPlays', '$totalPlays'] }, 100] }, 2] },
              0
            ]
          },
          risaComplianceRate: {
            $cond: [
              { $gt: ['$totalPlays', 0] },
              { $round: [{ $multiply: [{ $divide: ['$risaCompliantPlays', '$totalPlays'] }, 100] }, 2] },
              0
            ]
          },
          overallComplianceRate: {
            $cond: [
              { $gt: ['$totalPlays', 0] },
              { $round: [{ $multiply: [{ $divide: ['$fullyCompliantPlays', '$totalPlays'] }, 100] }, 2] },
              0
            ]
          }
        }
      }
    ];

    const metrics = await PlayHistory.aggregate(pipeline);
    const result = metrics[0] || {
      totalPlays: 0,
      totalTracks: 0,
      samroComplianceRate: 0,
      sampraComplianceRate: 0,
      risaComplianceRate: 0,
      overallComplianceRate: 0
    };

    res.json({
      success: true,
      data: result,
      metadata: {
        organization: allowedOrganization,
        filters: { startDate, endDate, storeId, organization: allowedOrganization },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching compliance metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch compliance metrics',
      details: error.message
    });
  }
};

// Get compliance alerts
exports.getComplianceAlerts = async (req, res) => {
  try {
    const { category, severity, status, limit = 20 } = req.query;

    const query = { isActive: true };

    if (category && category !== 'all') query.category = category;
    if (severity && severity !== 'all') query.severity = severity;
    if (status && status !== 'all') query.status = status;

    const ComplianceAlert = require('../models/ComplianceAlert.model');
    const alerts = await ComplianceAlert.find(query)
      .sort({ triggeredAt: -1 })
      .limit(parseInt(limit))
      .populate('assignedTo', 'username email')
      .populate('affectedEntities.tracks', 'title artist')
      .populate('affectedEntities.stores', 'name location');

    res.json({
      alerts,
      total: alerts.length
    });
  } catch (error) {
    console.error('Error fetching compliance alerts:', error);
    res.status(500).json({ error: 'Failed to fetch compliance alerts' });
  }
};

// Acknowledge compliance alert
exports.acknowledgeAlert = async (req, res) => {
  try {
    const { id } = req.params;
    const ComplianceAlert = require('../models/ComplianceAlert.model');

    const alert = await ComplianceAlert.findById(id);
    if (!alert) {
      return res.status(404).json({ error: 'Alert not found' });
    }

    alert.acknowledge(req.user.id);
    await alert.save();

    res.json({
      message: 'Alert acknowledged successfully',
      alert
    });
  } catch (error) {
    console.error('Error acknowledging alert:', error);
    res.status(500).json({ error: 'Failed to acknowledge alert' });
  }
};

// Resolve compliance alert
exports.resolveAlert = async (req, res) => {
  try {
    const { id } = req.params;
    const { resolution } = req.body;
    const ComplianceAlert = require('../models/ComplianceAlert.model');

    const alert = await ComplianceAlert.findById(id);
    if (!alert) {
      return res.status(404).json({ error: 'Alert not found' });
    }

    alert.status = 'resolved';
    alert.resolvedAt = new Date();
    alert.resolvedBy = req.user.id;
    alert.resolution = resolution;
    alert.updatedBy = req.user.id;

    await alert.save();

    res.json({
      message: 'Alert resolved successfully',
      alert
    });
  } catch (error) {
    console.error('Error resolving alert:', error);
    res.status(500).json({ error: 'Failed to resolve alert' });
  }
};

// Get licenses
exports.getLicenses = async (req, res) => {
  try {
    const { storeId, licenseType, status } = req.query;

    const query = {};
    if (storeId) query.storeId = storeId;
    if (licenseType) query.licenseType = licenseType;
    if (status) query.status = status;

    const VenueLicense = require('../models/VenueLicense.model');
    const licenses = await VenueLicense.find(query)
      .populate('storeId', 'name location')
      .populate('createdBy', 'username')
      .sort({ createdAt: -1 });

    res.json({
      licenses,
      total: licenses.length
    });
  } catch (error) {
    console.error('Error fetching licenses:', error);
    res.status(500).json({ error: 'Failed to fetch licenses' });
  }
};

// Create license
exports.createLicense = async (req, res) => {
  try {
    const VenueLicense = require('../models/VenueLicense.model');
    const licenseData = {
      ...req.body,
      createdBy: req.user.id,
      updatedBy: req.user.id
    };

    const license = new VenueLicense(licenseData);
    await license.save();

    res.status(201).json({
      message: 'License created successfully',
      license
    });
  } catch (error) {
    console.error('Error creating license:', error);
    res.status(500).json({ error: 'Failed to create license' });
  }
};

// Update license
exports.updateLicense = async (req, res) => {
  try {
    const { id } = req.params;
    const VenueLicense = require('../models/VenueLicense.model');

    const license = await VenueLicense.findById(id);
    if (!license) {
      return res.status(404).json({ error: 'License not found' });
    }

    // Additional organization-specific validation for updates
    const updateData = { ...req.body };

    // SAMRO staff can only update SAMRO-related fields
    if (req.user.role === 'samro_staff' || req.user.organization === 'SAMRO') {
      // Filter out SAMPRA-specific fields from update
      if (updateData.sampraLicense) {
        delete updateData.sampraLicense;
      }
      if (updateData.licenseType && updateData.licenseType !== 'SAMRO' && updateData.licenseType !== 'combined') {
        return res.status(403).json({ error: 'SAMRO staff cannot change license type to SAMPRA' });
      }
    }

    // SAMPRA staff can only update SAMPRA-related fields
    if (req.user.role === 'sampra_staff' || req.user.organization === 'SAMPRA') {
      // Filter out SAMRO-specific fields from update
      if (updateData.samroLicense) {
        delete updateData.samroLicense;
      }
      if (updateData.licenseType && updateData.licenseType !== 'SAMPRA' && updateData.licenseType !== 'combined') {
        return res.status(403).json({ error: 'SAMPRA staff cannot change license type to SAMRO' });
      }
    }

    Object.assign(license, updateData);
    license.updatedBy = req.user.id;

    // Add audit trail entry
    license.auditTrail.push({
      action: 'License Updated',
      performedBy: req.user.id,
      details: `License updated by ${req.user.username} (${req.user.role})`,
      timestamp: new Date()
    });

    await license.save();

    res.json({
      message: 'License updated successfully',
      license
    });
  } catch (error) {
    console.error('Error updating license:', error);
    res.status(500).json({ error: 'Failed to update license' });
  }
};

// Get single license
exports.getLicense = async (req, res) => {
  try {
    const { id } = req.params;
    const VenueLicense = require('../models/VenueLicense.model');

    const license = await VenueLicense.findById(id)
      .populate('storeId', 'name location')
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email');

    if (!license) {
      return res.status(404).json({ error: 'License not found' });
    }

    res.json(license);
  } catch (error) {
    console.error('Error fetching license:', error);
    res.status(500).json({ error: 'Failed to fetch license' });
  }
};

// Get royalty payments
exports.getRoyaltyPayments = async (req, res) => {
  try {
    const { storeId, organization, status, startDate, endDate } = req.query;

    const query = {};
    if (storeId) query['venue.storeId'] = storeId;
    if (organization) query.organization = organization;
    if (status) query.paymentStatus = status;
    if (startDate && endDate) {
      query['period.startDate'] = { $gte: new Date(startDate) };
      query['period.endDate'] = { $lte: new Date(endDate) };
    }

    const RoyaltyPayment = require('../models/RoyaltyPayment.model');
    const payments = await RoyaltyPayment.find(query)
      .populate('venue.storeId', 'name location')
      .populate('createdBy', 'username')
      .sort({ 'period.startDate': -1 });

    res.json({
      payments,
      total: payments.length
    });
  } catch (error) {
    console.error('Error fetching royalty payments:', error);
    res.status(500).json({ error: 'Failed to fetch royalty payments' });
  }
};

// Calculate royalties
exports.calculateRoyalties = async (req, res) => {
  try {
    const { storeId, organization, startDate, endDate, category } = req.body;

    // Get usage data for the period
    const PlayHistory = require('../models/PlayHistory.model');
    const Track = require('../models/Track.model');

    const usageData = await PlayHistory.aggregate([
      {
        $match: {
          storeId: mongoose.Types.ObjectId(storeId),
          playedDate: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $lookup: {
          from: 'tracks',
          localField: 'trackId',
          foreignField: '_id',
          as: 'track'
        }
      },
      { $unwind: '$track' },
      {
        $group: {
          _id: null,
          totalPlays: { $sum: 1 },
          totalDuration: { $sum: '$durationPlayed' },
          uniqueTracks: { $addToSet: '$trackId' },
          tracks: { $push: '$track' }
        }
      }
    ]);

    if (!usageData.length) {
      return res.status(400).json({ error: 'No usage data found for the specified period' });
    }

    const usage = usageData[0];

    // Get applicable rates
    let RateModel;
    if (organization === 'SAMRO') {
      RateModel = require('../models/SAMRORates.model');
    } else if (organization === 'SAMPRA') {
      RateModel = require('../models/SAMPRARates.model');
    } else {
      return res.status(400).json({ error: 'Invalid organization specified' });
    }

    const rates = await RateModel.getCurrentRates(category);
    if (!rates.length) {
      return res.status(400).json({ error: 'No applicable rates found' });
    }

    const rate = rates[0];

    // Calculate royalty
    let royaltyAmount = 0;
    if (organization === 'SAMRO') {
      royaltyAmount = rate.calculateRoyalty({
        plays: usage.totalPlays,
        operatingDays: Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24))
      });
    } else if (organization === 'SAMPRA') {
      royaltyAmount = rate.calculateNeedletimeRoyalty({
        plays: usage.totalPlays,
        needletimeDuration: usage.totalDuration
      });
    }

    // Create royalty payment record
    const RoyaltyPayment = require('../models/RoyaltyPayment.model');
    const Store = require('../models/Store.model');
    const store = await Store.findById(storeId);

    const royaltyPayment = new RoyaltyPayment({
      paymentId: `PAY-${organization}-${Date.now()}`,
      period: {
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        periodType: 'custom'
      },
      venue: {
        storeId: storeId,
        name: store.name
      },
      organization,
      financialSummary: {
        totalAmount: royaltyAmount.totalRoyalty || royaltyAmount,
        currency: 'ZAR',
        vatAmount: (royaltyAmount.vat || 0),
        netAmount: royaltyAmount.baseRoyalty || royaltyAmount
      },
      paymentStatus: 'pending',
      usageData: {
        totalTracks: usage.uniqueTracks.length,
        totalPlays: usage.totalPlays,
        totalDuration: usage.totalDuration,
        uniqueArtists: [...new Set(usage.tracks.map(t => t.artist))].length
      },
      createdBy: req.user.id
    });

    await royaltyPayment.save();

    res.json({
      message: 'Royalties calculated successfully',
      royaltyPayment,
      calculation: royaltyAmount
    });
  } catch (error) {
    console.error('Error calculating royalties:', error);
    res.status(500).json({ error: 'Failed to calculate royalties' });
  }
};

// Mark payment as paid
exports.markPaymentAsPaid = async (req, res) => {
  try {
    const { id } = req.params;
    const { paidDate, paymentMethod, transactionReference } = req.body;

    const RoyaltyPayment = require('../models/RoyaltyPayment.model');
    const payment = await RoyaltyPayment.findById(id);

    if (!payment) {
      return res.status(404).json({ error: 'Payment not found' });
    }

    payment.paymentStatus = 'paid';
    payment.paymentDetails = {
      paidDate: paidDate || new Date(),
      paymentMethod: paymentMethod || 'bank_transfer',
      transactionReference
    };
    payment.updatedBy = req.user.id;

    await payment.save();

    res.json({
      message: 'Payment marked as paid successfully',
      payment
    });
  } catch (error) {
    console.error('Error marking payment as paid:', error);
    res.status(500).json({ error: 'Failed to mark payment as paid' });
  }
};

// Get single royalty payment
exports.getRoyaltyPayment = async (req, res) => {
  try {
    const { id } = req.params;
    const RoyaltyPayment = require('../models/RoyaltyPayment.model');

    const payment = await RoyaltyPayment.findById(id)
      .populate('venue.storeId', 'name location')
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email');

    if (!payment) {
      return res.status(404).json({ error: 'Payment not found' });
    }

    res.json(payment);
  } catch (error) {
    console.error('Error fetching royalty payment:', error);
    res.status(500).json({ error: 'Failed to fetch royalty payment' });
  }
};

// ===== NEW COMPLIANCE VALIDATION ENDPOINTS =====

// Validate track compliance
exports.validateTrackCompliance = async (req, res) => {
  try {
    const { trackId } = req.params;

    const validationResult = await complianceValidationService.validateTrackCompliance(trackId);

    res.json({
      message: 'Track compliance validation completed',
      result: validationResult
    });
  } catch (error) {
    console.error('Error validating track compliance:', error);
    res.status(500).json({ error: 'Failed to validate track compliance' });
  }
};

// Get real-time compliance status
exports.getRealTimeComplianceStatus = async (req, res) => {
  try {
    const status = await realTimeComplianceService.getComplianceStatus();

    res.json(status);
  } catch (error) {
    console.error('Error fetching real-time compliance status:', error);
    res.status(500).json({ error: 'Failed to fetch compliance status' });
  }
};

// Generate missing metadata alerts
exports.generateMissingMetadataAlerts = async (req, res) => {
  try {
    const alerts = await complianceValidationService.generateMissingMetadataAlerts();

    res.json({
      message: 'Missing metadata alerts generated',
      alertsCreated: alerts.length,
      alerts
    });
  } catch (error) {
    console.error('Error generating missing metadata alerts:', error);
    res.status(500).json({ error: 'Failed to generate alerts' });
  }
};

// Run batch compliance validation
exports.runBatchValidation = async (req, res) => {
  try {
    const { limit = 100 } = req.query;

    const results = await complianceValidationService.runBatchValidation(parseInt(limit));

    res.json({
      message: 'Batch validation completed',
      tracksValidated: results.length,
      results
    });
  } catch (error) {
    console.error('Error running batch validation:', error);
    res.status(500).json({ error: 'Failed to run batch validation' });
  }
};

// Start real-time monitoring
exports.startRealTimeMonitoring = async (req, res) => {
  try {
    realTimeComplianceService.startMonitoring();

    res.json({
      message: 'Real-time compliance monitoring started',
      status: 'active'
    });
  } catch (error) {
    console.error('Error starting real-time monitoring:', error);
    res.status(500).json({ error: 'Failed to start monitoring' });
  }
};

// Stop real-time monitoring
exports.stopRealTimeMonitoring = async (req, res) => {
  try {
    realTimeComplianceService.stopMonitoring();

    res.json({
      message: 'Real-time compliance monitoring stopped',
      status: 'inactive'
    });
  } catch (error) {
    console.error('Error stopping real-time monitoring:', error);
    res.status(500).json({ error: 'Failed to stop monitoring' });
  }
};

// Generate SAMRO monthly return
exports.generateSAMROMonthlyReturn = async (req, res) => {
  try {
    const { storeId, startDate, endDate } = req.body;

    const report = await samroReportingService.generateMonthlyReturn({
      storeId,
      startDate,
      endDate
    });

    res.json({
      message: 'SAMRO monthly return generated successfully',
      report
    });
  } catch (error) {
    console.error('Error generating SAMRO monthly return:', error);
    res.status(500).json({ error: 'Failed to generate SAMRO report' });
  }
};

// Generate SAMPRA quarterly report
exports.generateSAMPRAQuarterlyReport = async (req, res) => {
  try {
    const { storeId, startDate, endDate, quarter, year } = req.body;

    const report = await sampraReportingService.generateQuarterlyReport({
      storeId,
      startDate,
      endDate,
      quarter,
      year
    });

    res.json({
      message: 'SAMPRA quarterly report generated successfully',
      report
    });
  } catch (error) {
    console.error('Error generating SAMPRA quarterly report:', error);
    res.status(500).json({ error: 'Failed to generate SAMPRA report' });
  }
};

// Export SAMRO report to XML
exports.exportSAMROReportXML = async (req, res) => {
  try {
    const { reportData } = req.body;

    const xmlContent = await samroReportingService.exportToSAMROXML(reportData);

    res.set({
      'Content-Type': 'application/xml',
      'Content-Disposition': `attachment; filename="samro_report_${Date.now()}.xml"`
    });

    res.send(xmlContent);
  } catch (error) {
    console.error('Error exporting SAMRO report to XML:', error);
    res.status(500).json({ error: 'Failed to export SAMRO report' });
  }
};

// Export SAMPRA report to CSV
exports.exportSAMPRAReportCSV = async (req, res) => {
  try {
    const { reportData } = req.body;

    const csvFilePath = await sampraReportingService.exportToSAMPRACSV(reportData);

    res.download(csvFilePath, `sampra_report_${Date.now()}.csv`);
  } catch (error) {
    console.error('Error exporting SAMPRA report to CSV:', error);
    res.status(500).json({ error: 'Failed to export SAMPRA report' });
  }
};

// Activity monitoring endpoints for compliance dashboard

// Get all compliance activities
exports.getAllComplianceActivities = async (req, res) => {
  try {
    console.log('getAllComplianceActivities called by user:', req.user?.role, req.user?.organization);
    const {
      page = 1,
      limit = 20,
      storeId,
      activityType,
      startDate,
      endDate,
      sourceFrom,
      sourceTo,
      requiresReporting
    } = req.query;

    console.log('Query params:', req.query);

    // Build query
    const query = {};

    if (storeId && storeId !== 'all') {
      query.storeId = storeId;
    }

    if (activityType && activityType !== 'all') {
      query.activityType = activityType;
    }

    if (sourceFrom && sourceFrom !== 'all') {
      query.sourceFrom = sourceFrom;
    }

    if (sourceTo) {
      query.sourceTo = sourceTo;
    }

    if (requiresReporting !== undefined) {
      query['compliance.requiresReporting'] = requiresReporting === 'true';
    }

    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate);
    }

    console.log('Final query:', JSON.stringify(query, null, 2));

    const activities = await ActivityLog.find(query)
      .populate('storeId', 'name location')
      .populate('musicPlayerData.trackId', 'title artist album')
      .populate('musicPlayerData.playlistId', 'name')
      .populate('radioData.stationId', 'name genre country')
      .populate('auditInfo.createdBy', 'name email')
      .sort({ timestamp: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await ActivityLog.countDocuments(query);

    console.log(`Found ${activities.length} activities out of ${total} total`);

    res.json({
      success: true,
      data: activities,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching compliance activities:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch compliance activities',
      error: error.message
    });
  }
};

// Get activity summary for compliance dashboard
exports.getComplianceActivitySummary = async (req, res) => {
  try {
    const { storeId, startDate, endDate } = req.query;

    // Build base query
    const baseQuery = {};

    if (storeId && storeId !== 'all') {
      baseQuery.storeId = storeId;
    }

    if (startDate || endDate) {
      baseQuery.timestamp = {};
      if (startDate) baseQuery.timestamp.$gte = new Date(startDate);
      if (endDate) baseQuery.timestamp.$lte = new Date(endDate);
    }

    // Get activity counts by type
    const activityCounts = await ActivityLog.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: '$activityType',
          count: { $sum: 1 },
          totalDuration: { $sum: '$duration' }
        }
      }
    ]);

    // Get compliance status summary
    const complianceSummary = await ActivityLog.aggregate([
      { $match: { ...baseQuery, 'compliance.requiresReporting': true } },
      {
        $group: {
          _id: null,
          totalRequiringReporting: { $sum: 1 },
          reportedToSAMRO: {
            $sum: { $cond: ['$compliance.reportedToSAMRO', 1, 0] }
          },
          reportedToSAMPRA: {
            $sum: { $cond: ['$compliance.reportedToSAMPRA', 1, 0] }
          },
          pending: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$compliance.reportedToSAMRO', false] },
                    { $eq: ['$compliance.reportedToSAMPRA', false] }
                  ]
                },
                1,
                0
              ]
            }
          }
        }
      }
    ]);

    // Get source switching statistics
    const sourceSwitchingStats = await ActivityLog.aggregate([
      { $match: { ...baseQuery, activityType: 'source_switch' } },
      {
        $group: {
          _id: {
            from: '$sourceFrom',
            to: '$sourceTo'
          },
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        activityCounts,
        complianceSummary: complianceSummary[0] || {
          totalRequiringReporting: 0,
          reportedToSAMRO: 0,
          reportedToSAMPRA: 0,
          pending: 0
        },
        sourceSwitchingStats
      }
    });
  } catch (error) {
    console.error('Error fetching activity summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch activity summary',
      error: error.message
    });
  }
};

// Mark activities as reported to compliance organizations
exports.markActivitiesAsReported = async (req, res) => {
  try {
    const { activityIds, organization } = req.body;

    if (!Array.isArray(activityIds) || activityIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Activity IDs are required'
      });
    }

    if (!['SAMRO', 'SAMPRA', 'RISA'].includes(organization)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid organization'
      });
    }

    const updateField = `compliance.reportedTo${organization}`;
    const result = await ActivityLog.updateMany(
      { _id: { $in: activityIds } },
      {
        $set: {
          [updateField]: true,
          'compliance.reportingDate': new Date(),
          'auditInfo.verifiedBy': req.user.id
        }
      }
    );

    res.json({
      success: true,
      message: `${result.modifiedCount} activities marked as reported to ${organization}`,
      modifiedCount: result.modifiedCount
    });
  } catch (error) {
    console.error('Error marking activities as reported:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark activities as reported',
      error: error.message
    });
  }
};
