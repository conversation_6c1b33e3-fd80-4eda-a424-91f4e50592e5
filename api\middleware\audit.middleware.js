const AuditLog = require('../models/AuditLog.model');

/**
 * Audit logging middleware and utilities
 */

// Middleware to automatically log certain actions
const auditLogger = (action, resourceType = null) => {
  return async (req, res, next) => {
    // Store original res.json to intercept response
    const originalJson = res.json;
    
    res.json = function(data) {
      // Only log successful operations (2xx status codes)
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // Don't wait for audit log to complete
        setImmediate(async () => {
          try {
            await logAuditAction({
              action,
              performedBy: req.user?.id,
              details: generateActionDetails(action, req, data),
              resourceType,
              resourceId: extractResourceId(req, data),
              ipAddress: req.ip || req.connection.remoteAddress || 'unknown',
              userAgent: req.get('User-Agent') || 'unknown',
              organization: req.user?.organization || 'ADMIN',
              metadata: {
                requestId: req.headers['x-request-id'] || generateRequestId(),
                sessionId: req.sessionID,
                riskLevel: determineRiskLevel(action),
                tags: generateTags(action, req)
              }
            });
          } catch (error) {
            console.error('Failed to log audit action:', error);
          }
        });
      }
      
      // Call original json method
      return originalJson.call(this, data);
    };
    
    next();
  };
};

// Manual audit logging function
const logAuditAction = async (auditData) => {
  try {
    // Validate required fields
    if (!auditData.action || !auditData.performedBy) {
      console.warn('Audit log missing required fields:', auditData);
      return null;
    }

    // Set defaults
    const logData = {
      ...auditData,
      timestamp: auditData.timestamp || new Date(),
      ipAddress: auditData.ipAddress || 'unknown',
      organization: auditData.organization || 'ADMIN',
      metadata: {
        riskLevel: 'low',
        ...auditData.metadata
      }
    };

    const auditLog = await AuditLog.logAction(logData);
    return auditLog;
  } catch (error) {
    console.error('Failed to create audit log:', error);
    return null;
  }
};

// Helper function to generate action details
const generateActionDetails = (action, req, responseData) => {
  const method = req.method;
  const url = req.originalUrl;
  
  switch (action) {
    case 'user_login':
      return `User logged in from ${req.ip}`;
    
    case 'report_generated':
    case 'report_created':
      const reportId = responseData?.reportId || responseData?.data?.reportId;
      return `Compliance report ${reportId} created via ${method} ${url}`;
    
    case 'report_verified':
      return `Compliance report verified via ${method} ${url}`;
    
    case 'data_export':
      const format = req.query?.format || 'unknown';
      return `Data exported in ${format} format via ${method} ${url}`;
    
    case 'settings_updated':
      const settingsChanged = Object.keys(req.body || {});
      return `System settings updated: ${settingsChanged.join(', ')} via ${method} ${url}`;
    
    case 'track_uploaded':
      const trackTitle = req.body?.title || responseData?.title || 'unknown';
      return `Track "${trackTitle}" uploaded via ${method} ${url}`;
    
    case 'user_created':
      const username = req.body?.username || responseData?.username || 'unknown';
      return `User "${username}" created via ${method} ${url}`;
    
    default:
      return `${action} performed via ${method} ${url}`;
  }
};

// Helper function to extract resource ID from request/response
const extractResourceId = (req, responseData) => {
  // Try to get ID from URL params
  if (req.params?.id) return req.params.id;
  
  // Try to get ID from response data
  if (responseData?.id) return responseData.id;
  if (responseData?._id) return responseData._id;
  if (responseData?.reportId) return responseData.reportId;
  if (responseData?.data?.id) return responseData.data.id;
  if (responseData?.data?._id) return responseData.data._id;
  if (responseData?.data?.reportId) return responseData.data.reportId;
  
  return null;
};

// Helper function to determine risk level
const determineRiskLevel = (action) => {
  const highRiskActions = [
    'user_deleted',
    'report_deleted',
    'track_deleted',
    'system_restore',
    'settings_updated'
  ];
  
  const mediumRiskActions = [
    'user_created',
    'user_updated',
    'password_changed',
    'report_verified',
    'data_export'
  ];
  
  if (highRiskActions.includes(action)) return 'high';
  if (mediumRiskActions.includes(action)) return 'medium';
  return 'low';
};

// Helper function to generate tags
const generateTags = (action, req) => {
  const tags = [];
  
  // Add action category tags
  if (action.startsWith('user_')) tags.push('user_management');
  if (action.startsWith('report_')) tags.push('compliance');
  if (action.startsWith('track_')) tags.push('content_management');
  if (action.startsWith('system_')) tags.push('system_administration');
  
  // Add method tag
  tags.push(req.method.toLowerCase());
  
  // Add organization tag if available
  if (req.user?.organization) {
    tags.push(req.user.organization.toLowerCase());
  }
  
  return tags;
};

// Helper function to generate request ID
const generateRequestId = () => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Specific audit logging functions for common actions
const auditActions = {
  userLogin: (userId, ipAddress, userAgent, organization) => 
    logAuditAction({
      action: 'user_login',
      performedBy: userId,
      details: `User logged in from ${ipAddress}`,
      ipAddress,
      userAgent,
      organization,
      metadata: { riskLevel: 'low', tags: ['authentication'] }
    }),

  userLogout: (userId, ipAddress, userAgent, organization) =>
    logAuditAction({
      action: 'user_logout',
      performedBy: userId,
      details: `User logged out from ${ipAddress}`,
      ipAddress,
      userAgent,
      organization,
      metadata: { riskLevel: 'low', tags: ['authentication'] }
    }),

  reportGenerated: (userId, reportId, reportType, organization, ipAddress, userAgent) =>
    logAuditAction({
      action: 'report_generated',
      performedBy: userId,
      details: `${reportType} compliance report ${reportId} generated`,
      resourceType: 'ComplianceReport',
      resourceId: reportId,
      ipAddress,
      userAgent,
      organization,
      metadata: { riskLevel: 'medium', tags: ['compliance', 'reporting'] }
    }),

  dataExported: (userId, exportType, format, organization, ipAddress, userAgent) =>
    logAuditAction({
      action: 'data_export',
      performedBy: userId,
      details: `${exportType} data exported in ${format} format`,
      ipAddress,
      userAgent,
      organization,
      metadata: { riskLevel: 'medium', tags: ['data_export', format] }
    }),

  settingsUpdated: (userId, settingsChanged, organization, ipAddress, userAgent) =>
    logAuditAction({
      action: 'settings_updated',
      performedBy: userId,
      details: `System settings updated: ${settingsChanged.join(', ')}`,
      resourceType: 'Settings',
      ipAddress,
      userAgent,
      organization,
      metadata: {
        riskLevel: 'high',
        tags: ['system_administration', 'configuration'],
        oldValues: settingsChanged.old,
        newValues: settingsChanged.new
      }
    }),

  profileUpdate: (userId, ipAddress, userAgent, organization, changes) =>
    logAuditAction({
      action: 'profile_updated',
      performedBy: userId,
      details: `User profile updated from ${ipAddress}`,
      resourceType: 'User',
      resourceId: userId,
      ipAddress,
      userAgent,
      organization,
      metadata: {
        riskLevel: 'medium',
        tags: ['user_management', 'profile'],
        changes
      }
    }),

  passwordChange: (userId, ipAddress, userAgent, organization) =>
    logAuditAction({
      action: 'password_changed',
      performedBy: userId,
      details: `User password changed from ${ipAddress}`,
      resourceType: 'User',
      resourceId: userId,
      ipAddress,
      userAgent,
      organization,
      metadata: {
        riskLevel: 'medium',
        tags: ['user_management', 'security', 'authentication']
      }
    }),

  // Spotify integration audit actions
  spotifyConnection: (userId, ipAddress, userAgent, action) =>
    logAuditAction({
      action: `spotify_${action}`,
      performedBy: userId,
      details: `Spotify account ${action}`,
      ipAddress,
      userAgent,
      metadata: {
        riskLevel: 'low',
        tags: ['spotify', 'integration', 'third_party']
      }
    }),

  spotifyPlaybackControl: (userId, action, data, ipAddress) =>
    logAuditAction({
      action: 'spotify_playback_control',
      performedBy: userId,
      details: `Spotify playback ${action} executed`,
      ipAddress,
      metadata: {
        riskLevel: 'low',
        tags: ['spotify', 'playback', 'music_control'],
        playbackAction: action,
        playbackData: data
      }
    })
};

module.exports = {
  auditLogger,
  logAuditAction,
  auditActions,
  generateActionDetails,
  extractResourceId,
  determineRiskLevel,
  generateTags
};
