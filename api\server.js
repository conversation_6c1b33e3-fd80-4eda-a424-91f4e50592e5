const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

// Load environment variables FIRST
dotenv.config();

// Debug: Check if environment variables are loaded
console.log('MONGO_URI:', process.env.MONGO_URI);
console.log('JWT_SECRET:', process.env.JWT_SECRET ? 'Loaded' : 'Not loaded');

const app = express();
const connectDB = require('./config/db');

connectDB();

// CORS configuration
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000', 'http://127.0.0.1:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json());

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    service: 'TrakSong API'
  });
});

// Serve static files (uploaded tracks and reports)
app.use('/uploads', express.static('uploads'));
app.use('/api/reports', express.static('reports')); // Serve reports statically

// Ensure reports directory exists
const fs = require('fs');
const path = require('path');
const reportsDir = path.join(__dirname, 'reports');
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir, { recursive: true });
  console.log('Created reports directory');
}

app.use('/api/auth', require('./routes/auth.routes.js'));
app.use('/api/registration', require('./routes/registration.routes.js'));
app.use('/api/tracks', require('./routes/track.routes.js'));
app.use('/api/playlists', require('./routes/playlist.routes.js'));
app.use('/api/history', require('./routes/history.routes.js'));
app.use('/api/reports', require('./routes/report.routes.js'));
app.use('/api/admin', require('./routes/admin.routes.js'));
app.use('/api/stores', require('./routes/store.routes.js'));
app.use('/api/radio', require('./routes/radio.routes.js'));
app.use('/api/compliance', require('./routes/compliance.routes.js'));
app.use('/api/activity', require('./routes/activity.routes.js'));
app.use('/api/sampra', require('./routes/sampra.routes.js'));
app.use('/api/spotify', require('./routes/spotify.routes.js'));
app.use('/api/samro', require('./routes/samro.routes.js'));
app.use('/api/venue-licenses', require('./routes/venueLicense.routes.js'));
app.use('/api/messages', require('./routes/message.routes.js'));
app.use('/api/events', require('./routes/event.routes.js'));
app.use('/api/account', require('./routes/account.routes.js'));


const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));