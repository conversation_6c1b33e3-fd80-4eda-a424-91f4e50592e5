# Spotify Integration Setup Guide

This guide will help you set up the Spotify integration for the TrakSong music compliance system.

## Prerequisites

1. **Spotify Developer Account**: You need a Spotify Developer account to create an application
2. **TrakSong System**: The TrakSong system should be running and accessible
3. **HTTPS**: For production, you'll need HTTPS enabled (Spotify requires secure redirect URIs)

## Step 1: Create Spotify Application

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Log in with your Spotify account
3. Click "Create an App"
4. Fill in the application details:
   - **App Name**: TrakSong Music Compliance System
   - **App Description**: Music compliance and tracking system for retail stores
   - **Website**: Your TrakSong domain (e.g., https://yourdomain.com)
   - **Redirect URI**: `https://yourdomain.com/store` (or `http://localhost:3000/store` for development)
5. Accept the terms and create the app
6. Note down your **Client ID** and **Client Secret**

## Step 2: Configure Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Spotify Integration
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here
SPOTIFY_REDIRECT_URI=https://yourdomain.com/store

# Encryption Key (for storing sensitive tokens)
ENCRYPTION_KEY=your_strong_encryption_key_here
```

### Development Environment
For development, use:
```bash
SPOTIFY_REDIRECT_URI=http://localhost:3000/store
```

### Production Environment
For production, use your actual domain:
```bash
SPOTIFY_REDIRECT_URI=https://yourdomain.com/store
```

## Step 3: Generate Encryption Key

Generate a strong encryption key for storing Spotify tokens securely:

```bash
# Using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Using OpenSSL
openssl rand -hex 32
```

## Step 4: Update Spotify App Settings

In your Spotify Developer Dashboard:

1. Go to your app settings
2. Add redirect URIs:
   - Development: `http://localhost:3000/store`
   - Production: `https://yourdomain.com/store`
3. Set the following scopes (these are requested automatically by the integration):
   - `user-read-playback-state`: Read current playback state
   - `user-modify-playback-state`: Control playback
   - `user-read-currently-playing`: Read currently playing track
   - `user-read-recently-played`: Read recently played tracks
   - `streaming`: Play music through Web Playback SDK

## Step 5: Test the Integration

1. **Start the TrakSong system**:
   ```bash
   cd api && npm start
   cd ui && npm start
   ```

2. **Access the Store Dashboard**:
   - Log in as a store user
   - Navigate to "Spotify Integration" tab

3. **Connect Spotify Account**:
   - Click "Connect Spotify"
   - You'll be redirected to Spotify for authorization
   - Grant the requested permissions
   - You'll be redirected back to TrakSong

4. **Test Playback Control**:
   - Start playing music on any Spotify device
   - The TrakSong player should show the current track
   - Test play/pause, next/previous controls

## Step 6: Verify Compliance Logging

1. **Check Activity Logs**:
   - Play some tracks through Spotify
   - Check the compliance dashboard for logged activities
   - Verify that Spotify plays appear in compliance reports

2. **Generate Test Report**:
   - Go to Compliance Dashboard
   - Generate a compliance report for the test period
   - Verify that Spotify plays are included with proper metadata

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI" error**:
   - Ensure the redirect URI in your Spotify app matches exactly with your environment variable
   - Check for trailing slashes or protocol mismatches

2. **"Invalid client" error**:
   - Verify your Client ID and Client Secret are correct
   - Ensure there are no extra spaces in your environment variables

3. **Token encryption errors**:
   - Verify your encryption key is properly set
   - Ensure the key is at least 32 characters long

4. **Playback control not working**:
   - Ensure the user has Spotify Premium (required for full playback control)
   - Check that Spotify is actively playing on a device

### Debug Mode

Enable debug logging by setting:
```bash
DEBUG=spotify:*
NODE_ENV=development
```

### API Testing

Test the Spotify API endpoints directly:

```bash
# Get auth URL
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:5000/api/spotify/auth-url

# Check status
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:5000/api/spotify/status
```

## Security Considerations

1. **HTTPS in Production**: Always use HTTPS in production environments
2. **Token Storage**: Spotify tokens are encrypted before storage in the database
3. **Scope Limitation**: Only request necessary Spotify scopes
4. **Regular Token Refresh**: The system automatically refreshes expired tokens
5. **User Consent**: Users must explicitly connect their Spotify accounts

## Compliance Features

The Spotify integration provides the following compliance features:

1. **Real-time Logging**: All Spotify playback is logged in real-time
2. **ISRC Tracking**: ISRC codes are captured from Spotify metadata
3. **Device Tracking**: Information about playback devices is logged
4. **Context Tracking**: Playlist/album context is captured
5. **Duration Tracking**: Actual play duration is estimated and logged
6. **Report Integration**: Spotify plays are included in SAMRO/SAMPRA compliance reports

## Limitations

1. **Premium Required**: Full playback control requires Spotify Premium
2. **Active Session**: Can only log tracks when user is actively using TrakSong
3. **Limited Metadata**: Some compliance metadata (composers, publishers) may not be available from Spotify
4. **Rate Limits**: Spotify API has rate limits that may affect high-volume usage

## Support

For issues with the Spotify integration:

1. Check the application logs for error messages
2. Verify all environment variables are set correctly
3. Test with a simple Spotify Premium account first
4. Contact TrakSong support with specific error messages and steps to reproduce

## API Reference

The Spotify integration exposes the following API endpoints:

- `GET /api/spotify/auth-url` - Get authorization URL
- `POST /api/spotify/callback` - Handle OAuth callback
- `GET /api/spotify/status` - Get integration status
- `GET /api/spotify/playback` - Get current playback state
- `POST /api/spotify/playback/:action` - Control playback
- `POST /api/spotify/log-current` - Log current track
- `POST /api/spotify/sync-recent` - Sync recent tracks
- `POST /api/spotify/disconnect` - Disconnect integration

All endpoints require authentication and return JSON responses.
