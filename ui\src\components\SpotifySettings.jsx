import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  FormControlLabel,
  Switch,
  Typography,
  Box,
  Divider,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Chip
} from '@mui/material';
import {
  Devices as DevicesIcon,
  MusicNote as MusicIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import spotifyService from '../services/spotifyService';

const SpotifySettings = ({ open, onClose, onSettingsChange }) => {
  const [settings, setSettings] = useState({
    autoLog: true,
    volumeSync: false,
    crossfade: false,
    enableLogging: true,
    logPrivateSessions: false,
    includeInReports: true,
    trackExplicitContent: true
  });
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (open) {
      loadDevices();
      loadSettings();
    }
  }, [open]);

  const loadDevices = async () => {
    try {
      const devicesData = await spotifyService.getDevices();
      setDevices(devicesData.devices || []);
    } catch (error) {
      console.error('Failed to load devices:', error);
      setError('Failed to load Spotify devices');
    }
  };

  const loadSettings = async () => {
    try {
      const status = await spotifyService.getStatus();
      // In a real implementation, you'd load these from the user's profile
      // For now, we'll use default values
      setSettings({
        autoLog: true,
        volumeSync: false,
        crossfade: false,
        enableLogging: true,
        logPrivateSessions: false,
        includeInReports: true,
        trackExplicitContent: true
      });
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const handleSettingChange = (setting) => (event) => {
    setSettings(prev => ({
      ...prev,
      [setting]: event.target.checked
    }));
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      // In a real implementation, you'd save these settings to the user's profile
      // For now, we'll just close the dialog
      
      if (onSettingsChange) {
        onSettingsChange();
      }
      onClose();
    } catch (error) {
      console.error('Failed to save settings:', error);
      setError('Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSyncNow = async () => {
    try {
      setLoading(true);
      await spotifyService.syncRecentTracks();
      setError(null);
      // Show success message or update UI
    } catch (error) {
      console.error('Failed to sync tracks:', error);
      setError('Failed to sync recent tracks');
    } finally {
      setLoading(false);
    }
  };

  const getDeviceIcon = (deviceType) => {
    switch (deviceType?.toLowerCase()) {
      case 'computer':
        return '💻';
      case 'smartphone':
        return '📱';
      case 'speaker':
        return '🔊';
      case 'tv':
        return '📺';
      default:
        return '🎵';
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={2}>
          <MusicIcon color="primary" />
          Spotify Integration Settings
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Playback Settings */}
        <Box mb={3}>
          <Typography variant="h6" gutterBottom>
            <Box display="flex" alignItems="center" gap={1}>
              <MusicIcon />
              Playback Settings
            </Box>
          </Typography>
          
          <FormControlLabel
            control={
              <Switch
                checked={settings.autoLog}
                onChange={handleSettingChange('autoLog')}
              />
            }
            label="Automatically log played tracks"
          />
          <Typography variant="caption" color="text.secondary" display="block">
            Automatically log tracks for compliance when they're played through Spotify
          </Typography>

          <FormControlLabel
            control={
              <Switch
                checked={settings.volumeSync}
                onChange={handleSettingChange('volumeSync')}
              />
            }
            label="Sync volume with TrakSong player"
          />
          <Typography variant="caption" color="text.secondary" display="block">
            Keep Spotify volume in sync with the main TrakSong player
          </Typography>

          <FormControlLabel
            control={
              <Switch
                checked={settings.crossfade}
                onChange={handleSettingChange('crossfade')}
              />
            }
            label="Enable crossfade"
          />
          <Typography variant="caption" color="text.secondary" display="block">
            Smooth transitions between tracks
          </Typography>
        </Box>

        <Divider />

        {/* Compliance Settings */}
        <Box my={3}>
          <Typography variant="h6" gutterBottom>
            <Box display="flex" alignItems="center" gap={1}>
              <SecurityIcon />
              Compliance Settings
            </Box>
          </Typography>
          
          <FormControlLabel
            control={
              <Switch
                checked={settings.enableLogging}
                onChange={handleSettingChange('enableLogging')}
              />
            }
            label="Enable compliance logging"
          />
          <Typography variant="caption" color="text.secondary" display="block">
            Log all Spotify playback for SAMRO/SAMPRA compliance reporting
          </Typography>

          <FormControlLabel
            control={
              <Switch
                checked={settings.logPrivateSessions}
                onChange={handleSettingChange('logPrivateSessions')}
              />
            }
            label="Log private listening sessions"
          />
          <Typography variant="caption" color="text.secondary" display="block">
            Include tracks played during private sessions in compliance logs
          </Typography>

          <FormControlLabel
            control={
              <Switch
                checked={settings.includeInReports}
                onChange={handleSettingChange('includeInReports')}
              />
            }
            label="Include in compliance reports"
          />
          <Typography variant="caption" color="text.secondary" display="block">
            Include Spotify plays in generated compliance reports
          </Typography>

          <FormControlLabel
            control={
              <Switch
                checked={settings.trackExplicitContent}
                onChange={handleSettingChange('trackExplicitContent')}
              />
            }
            label="Track explicit content"
          />
          <Typography variant="caption" color="text.secondary" display="block">
            Log and report explicit content flags for compliance
          </Typography>

          <Box mt={2}>
            <Button
              variant="outlined"
              onClick={handleSyncNow}
              disabled={loading}
              size="small"
            >
              Sync Recent Tracks Now
            </Button>
            <Typography variant="caption" color="text.secondary" display="block" mt={1}>
              Manually sync recently played tracks for compliance logging
            </Typography>
          </Box>
        </Box>

        <Divider />

        {/* Available Devices */}
        <Box mt={3}>
          <Typography variant="h6" gutterBottom>
            <Box display="flex" alignItems="center" gap={1}>
              <DevicesIcon />
              Available Devices
            </Box>
          </Typography>
          
          {devices.length > 0 ? (
            <List dense>
              {devices.map((device) => (
                <ListItem key={device.id}>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <span>{getDeviceIcon(device.type)}</span>
                        {device.name}
                      </Box>
                    }
                    secondary={`${device.type} • Volume: ${device.volume_percent}%`}
                  />
                  <ListItemSecondaryAction>
                    {device.is_active && (
                      <Chip label="Active" color="primary" size="small" />
                    )}
                    {device.is_restricted && (
                      <Chip label="Restricted" color="warning" size="small" />
                    )}
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No devices found. Make sure Spotify is open on at least one device.
            </Typography>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button 
          onClick={handleSave} 
          variant="contained" 
          disabled={loading}
        >
          Save Settings
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SpotifySettings;
