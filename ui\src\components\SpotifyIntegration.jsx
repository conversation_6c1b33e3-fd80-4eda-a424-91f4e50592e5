import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
  Chip,
  Grid,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  MusicNote as SpotifyIcon,
  Link as ConnectIcon,
  LinkOff as DisconnectIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import spotifyService from '../services/spotifyService';
import SpotifyPlayer from './SpotifyPlayer';
import SpotifySettings from './SpotifySettings';

const SpotifyIntegration = () => {
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState(false);
  const [error, setError] = useState(null);
  const [showSettings, setShowSettings] = useState(false);
  const [autoSync, setAutoSync] = useState(false);

  useEffect(() => {
    loadStatus();
  }, []);

  useEffect(() => {
    // Handle OAuth callback if present in URL
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    const error = urlParams.get('error');

    if (code || error) {
      handleOAuthCallback(code, state, error);
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  const loadStatus = async () => {
    try {
      setLoading(true);
      const statusData = await spotifyService.getStatus();
      setStatus(statusData);
      setAutoSync(statusData.isConnected);
    } catch (error) {
      console.error('Failed to load Spotify status:', error);
      setError('Failed to load Spotify integration status');
    } finally {
      setLoading(false);
    }
  };

  const handleOAuthCallback = async (code, state, error) => {
    try {
      setConnecting(true);
      if (error) {
        throw new Error(`Spotify authorization failed: ${error}`);
      }

      const result = await spotifyService.handleCallback(code, state, error);
      if (result.success) {
        setError(null);
        await loadStatus();
      }
    } catch (error) {
      console.error('OAuth callback failed:', error);
      setError(error.message || 'Failed to connect Spotify account');
    } finally {
      setConnecting(false);
    }
  };

  const handleConnect = async () => {
    try {
      setConnecting(true);
      setError(null);
      
      const authData = await spotifyService.getAuthUrl();
      window.location.href = authData.authUrl;
    } catch (error) {
      console.error('Failed to initiate Spotify connection:', error);
      setError('Failed to initiate Spotify connection');
      setConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    try {
      setLoading(true);
      await spotifyService.disconnect();
      await loadStatus();
      setAutoSync(false);
      spotifyService.stopPeriodicSync();
    } catch (error) {
      console.error('Failed to disconnect Spotify:', error);
      setError('Failed to disconnect Spotify');
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshToken = async () => {
    try {
      setLoading(true);
      await spotifyService.refreshToken();
      await loadStatus();
      setError(null);
    } catch (error) {
      console.error('Failed to refresh token:', error);
      setError('Failed to refresh Spotify token');
    } finally {
      setLoading(false);
    }
  };

  const handleAutoSyncToggle = (event) => {
    const enabled = event.target.checked;
    setAutoSync(enabled);
    
    if (enabled && status?.isConnected) {
      spotifyService.startPeriodicSync();
    } else {
      spotifyService.stopPeriodicSync();
    }
  };

  const getStatusColor = () => {
    if (!status?.isConnected) return 'default';
    if (!status?.hasValidToken) return 'warning';
    return 'success';
  };

  const getStatusText = () => {
    if (!status?.isConnected) return 'Not Connected';
    if (!status?.hasValidToken) return 'Token Expired';
    return 'Connected';
  };

  const getStatusIcon = () => {
    if (!status?.isConnected) return <ErrorIcon />;
    if (!status?.hasValidToken) return <ErrorIcon />;
    return <CheckIcon />;
  };

  if (loading && !status) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Card>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
            <Box display="flex" alignItems="center" gap={2}>
              <SpotifyIcon sx={{ fontSize: 32, color: '#1DB954' }} />
              <Typography variant="h6">Spotify Integration</Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <Chip
                icon={getStatusIcon()}
                label={getStatusText()}
                color={getStatusColor()}
                variant="outlined"
              />
              {status?.isConnected && (
                <Tooltip title="Settings">
                  <IconButton onClick={() => setShowSettings(true)}>
                    <SettingsIcon />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
              {error}
            </Alert>
          )}

          {!status?.isConnected ? (
            <Box textAlign="center" py={3}>
              <Typography variant="body1" color="text.secondary" mb={3}>
                Connect your Spotify account to enable music streaming and automatic compliance logging.
              </Typography>
              <Button
                variant="contained"
                startIcon={<ConnectIcon />}
                onClick={handleConnect}
                disabled={connecting}
                sx={{ 
                  backgroundColor: '#1DB954',
                  '&:hover': { backgroundColor: '#1ed760' }
                }}
              >
                {connecting ? 'Connecting...' : 'Connect Spotify'}
              </Button>
            </Box>
          ) : (
            <Box>
              <Grid container spacing={2} mb={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Account
                  </Typography>
                  <Typography variant="body1">
                    {status.displayName || status.email}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {status.product === 'premium' ? 'Premium Account' : 'Free Account'}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Connected Since
                  </Typography>
                  <Typography variant="body1">
                    {status.connectionDate ? 
                      new Date(status.connectionDate).toLocaleDateString() : 
                      'Unknown'
                    }
                  </Typography>
                </Grid>
              </Grid>

              {status.product !== 'premium' && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    Spotify Premium is required for full playback control. 
                    Free accounts can only log currently playing tracks.
                  </Typography>
                </Alert>
              )}

              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={autoSync}
                      onChange={handleAutoSyncToggle}
                      disabled={!status.isConnected}
                    />
                  }
                  label="Auto-sync for compliance logging"
                />
                <Box>
                  <Tooltip title="Refresh Token">
                    <IconButton onClick={handleRefreshToken} disabled={loading}>
                      <RefreshIcon />
                    </IconButton>
                  </Tooltip>
                  <Button
                    variant="outlined"
                    startIcon={<DisconnectIcon />}
                    onClick={handleDisconnect}
                    disabled={loading}
                    color="error"
                    size="small"
                  >
                    Disconnect
                  </Button>
                </Box>
              </Box>

              {status.stats && (
                <Box>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="subtitle2" mb={1}>Statistics</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">
                        Tracks Played
                      </Typography>
                      <Typography variant="h6">
                        {status.stats.totalTracksPlayed || 0}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">
                        Total Play Time
                      </Typography>
                      <Typography variant="h6">
                        {Math.round((status.stats.totalPlayTime || 0) / 3600)}h
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}
            </Box>
          )}
        </CardContent>
      </Card>

      {status?.isConnected && status?.product === 'premium' && (
        <Box mt={2}>
          <SpotifyPlayer />
        </Box>
      )}

      {showSettings && (
        <SpotifySettings
          open={showSettings}
          onClose={() => setShowSettings(false)}
          onSettingsChange={loadStatus}
        />
      )}
    </Box>
  );
};

export default SpotifyIntegration;
