import api from './api';

class SpotifyService {
  // Get Spotify authorization URL
  async getAuthUrl(scopes = []) {
    try {
      const response = await api.get('/spotify/auth-url', {
        params: { scopes: scopes.join(',') }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get Spotify auth URL:', error);
      throw error;
    }
  }

  // Handle OAuth callback
  async handleCallback(code, state, error) {
    try {
      const response = await api.post('/spotify/callback', {
        code,
        state,
        error
      });
      return response.data;
    } catch (error) {
      console.error('Spotify callback failed:', error);
      throw error;
    }
  }

  // Get integration status
  async getStatus() {
    try {
      const response = await api.get('/spotify/status');
      return response.data;
    } catch (error) {
      console.error('Failed to get Spotify status:', error);
      throw error;
    }
  }

  // Get current playback state
  async getPlayback() {
    try {
      const response = await api.get('/spotify/playback');
      return response.data;
    } catch (error) {
      console.error('Failed to get playback state:', error);
      throw error;
    }
  }

  // Get currently playing track
  async getCurrentlyPlaying() {
    try {
      const response = await api.get('/spotify/currently-playing');
      return response.data;
    } catch (error) {
      console.error('Failed to get currently playing:', error);
      throw error;
    }
  }

  // Get user's devices
  async getDevices() {
    try {
      const response = await api.get('/spotify/devices');
      return response.data;
    } catch (error) {
      console.error('Failed to get devices:', error);
      throw error;
    }
  }

  // Control playback
  async controlPlayback(action, data = {}) {
    try {
      const response = await api.post(`/spotify/playback/${action}`, data);
      return response.data;
    } catch (error) {
      console.error(`Failed to ${action} playback:`, error);
      throw error;
    }
  }

  // Playback control methods
  async play(data = {}) {
    return this.controlPlayback('play', data);
  }

  async pause() {
    return this.controlPlayback('pause');
  }

  async next() {
    return this.controlPlayback('next');
  }

  async previous() {
    return this.controlPlayback('previous');
  }

  async seek(positionMs) {
    return this.controlPlayback('seek', { position_ms: positionMs });
  }

  async setVolume(volumePercent) {
    return this.controlPlayback('volume', { volume_percent: volumePercent });
  }

  async setShuffle(state) {
    return this.controlPlayback('shuffle', { state });
  }

  async setRepeat(state) {
    return this.controlPlayback('repeat', { state });
  }

  // Log current track for compliance
  async logCurrentTrack() {
    try {
      const response = await api.post('/spotify/log-current');
      return response.data;
    } catch (error) {
      console.error('Failed to log current track:', error);
      throw error;
    }
  }

  // Sync recently played tracks
  async syncRecentTracks() {
    try {
      const response = await api.post('/spotify/sync-recent');
      return response.data;
    } catch (error) {
      console.error('Failed to sync recent tracks:', error);
      throw error;
    }
  }

  // Get recently played tracks
  async getRecentTracks(limit = 20) {
    try {
      const response = await api.get('/spotify/recent', {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get recent tracks:', error);
      throw error;
    }
  }

  // Disconnect Spotify
  async disconnect() {
    try {
      const response = await api.post('/spotify/disconnect');
      return response.data;
    } catch (error) {
      console.error('Failed to disconnect Spotify:', error);
      throw error;
    }
  }

  // Refresh token
  async refreshToken() {
    try {
      const response = await api.post('/spotify/refresh-token');
      return response.data;
    } catch (error) {
      console.error('Failed to refresh token:', error);
      throw error;
    }
  }

  // Utility methods
  formatDuration(ms) {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  formatArtists(artists) {
    return artists.map(artist => artist.name).join(', ');
  }

  getTrackImage(track, size = 'medium') {
    if (!track.album?.images?.length) return null;
    
    const sizeMap = {
      small: 64,
      medium: 300,
      large: 640
    };
    
    const targetSize = sizeMap[size] || 300;
    
    // Find the closest image size
    const sortedImages = track.album.images.sort((a, b) => 
      Math.abs(a.width - targetSize) - Math.abs(b.width - targetSize)
    );
    
    return sortedImages[0]?.url;
  }

  // Check if user has premium account (required for full playback control)
  async checkPremiumStatus() {
    try {
      const status = await this.getStatus();
      return status.product === 'premium';
    } catch (error) {
      console.error('Failed to check premium status:', error);
      return false;
    }
  }

  // Start periodic sync for compliance logging
  startPeriodicSync(intervalMs = 30000) {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(async () => {
      try {
        const status = await this.getStatus();
        if (status.isConnected) {
          await this.logCurrentTrack();
        }
      } catch (error) {
        console.error('Periodic sync failed:', error);
      }
    }, intervalMs);

    return this.syncInterval;
  }

  // Stop periodic sync
  stopPeriodicSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  // Handle Spotify Web Playback SDK initialization
  initializeWebPlayback(token, deviceName = 'TrakSong Web Player') {
    return new Promise((resolve, reject) => {
      if (!window.Spotify) {
        reject(new Error('Spotify Web Playback SDK not loaded'));
        return;
      }

      const player = new window.Spotify.Player({
        name: deviceName,
        getOAuthToken: cb => { cb(token); },
        volume: 0.5
      });

      // Error handling
      player.addListener('initialization_error', ({ message }) => {
        console.error('Spotify player initialization error:', message);
        reject(new Error(message));
      });

      player.addListener('authentication_error', ({ message }) => {
        console.error('Spotify player authentication error:', message);
        reject(new Error(message));
      });

      player.addListener('account_error', ({ message }) => {
        console.error('Spotify player account error:', message);
        reject(new Error(message));
      });

      player.addListener('playback_error', ({ message }) => {
        console.error('Spotify player playback error:', message);
      });

      // Ready
      player.addListener('ready', ({ device_id }) => {
        console.log('Spotify player ready with device ID:', device_id);
        resolve({ player, deviceId: device_id });
      });

      // Not ready
      player.addListener('not_ready', ({ device_id }) => {
        console.log('Spotify player not ready with device ID:', device_id);
      });

      // Connect to the player
      player.connect();
    });
  }
}

export default new SpotifyService();
